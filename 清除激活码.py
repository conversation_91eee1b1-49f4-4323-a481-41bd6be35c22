#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
激活信息清理工具 - 用于开发测试

此工具用于清理已保存的激活信息，方便开发者测试激活流程。
"""

import os
import sys
import shutil

# 导入激活工具模块中的路径获取函数
try:
    from activation_utils import _get_activation_data_path
    DIRECT_IMPORT = True
except ImportError:
    DIRECT_IMPORT = False

def get_activation_file_path():
    """获取激活数据文件路径"""
    if DIRECT_IMPORT:
        # 直接从模块导入成功，使用模块函数
        return _get_activation_data_path()
    else:
        # 如果无法直接导入，使用与activation_utils相同的逻辑重新实现
        if sys.platform == "win32":
            app_data = os.environ.get("LOCALAPPDATA", os.environ.get("APPDATA", ""))
            if not app_data:
                app_data = os.path.expanduser("~")
        else:
            app_data = os.path.expanduser("~")
        
        # 特定于应用的目录路径
        app_dir = os.path.join(app_data, ".muoucfg")
        file_name = "app_settings.dat"
        return os.path.join(app_dir, file_name)

def clear_activation_data():
    """清理激活数据文件"""
    try:
        activation_file = get_activation_file_path()
        print(f"激活数据文件路径: {activation_file}")
        
        if os.path.exists(activation_file):
            # 删除激活数据文件
            os.remove(activation_file)
            print(f"已成功删除激活数据文件")
            return True
        else:
            print("未找到激活数据文件，软件可能尚未激活")
            return False
    except Exception as e:
        print(f"清理激活数据时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("============= 激活信息清理工具 =============")
    print("此工具用于清理已保存的激活信息，方便开发者测试激活流程。")
    
    # 请求确认
    confirm = input("确定要清除激活信息吗? (y/n): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    # 清理激活数据
    success = clear_activation_data()
    
    if success:
        print("\n激活信息已清除。下次启动应用时，将显示激活对话框。")
    else:
        print("\n提示：如果清理失败，请确保应用未在运行，然后重试。")

if __name__ == "__main__":
    main() 