# 木偶AI翻唱 (MuOuCover)

一个功能强大的AI音色转换应用，使用现代深色UI界面，提供用户友好的操作体验。

## 功能概述

MuOuCover应用允许用户：
1. 上传源音频文件（支持拖放和点击选择）并可视化音频波形
2. 从下拉菜单选择AI音色模型和配置文件
3. 配置音色转换参数，包括人声音高、伴奏音高、声码器类型等
4. 调整详细的混响参数（房间大小、阻尼、湿度等）
5. 一键执行处理流程并获取带波形预览的输出结果

## 环境要求

- Python 3.8+
- Windows操作系统 (设计为Windows平台应用)

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行原型

```bash
python app.py
```

## 界面说明

应用界面分为四个主要功能区域：

1. **准备与配置音频输入**
   - 拖放或浏览选择源音频文件
   - 音频波形可视化显示
   - 选择音频分离模式（Full或Remove Harmony & Reverb）

2. **配置音色转换参数**
   - 下拉选择AI音色模型和配置文件
   - 调整人声音高和伴奏音高
   - 选择声码器类型
   - 配置F0提取器、采样步数、采样器等参数
   - 调节共振峰偏移
   - 选择是否将和声加入伴奏

3. **混响设置**
   - 控制混响参数（房间大小、阻尼、湿度、干度、宽度）

4. **执行处理与获取结果**
   - 一键制作歌曲
   - 处理进度实时显示
   - 五个输出音频文件的波形预览和播放/下载功能：
     - 混缩后音频
     - 分离人声
     - 转换人声
     - 分离伴奏
     - 分离和声

## 设计说明

- **UI框架**: 使用CustomTkinter框架，提供现代化、美观的UI组件
- **布局**: 采用垂直滚动的单页面布局，各功能区域清晰分隔
- **交互增强**: 支持文件拖放和音频波形可视化
- **参数联动**: 选择模型时自动匹配配置文件
- **反馈机制**: 通过进度条、状态文本和波形预览提供处理反馈

## 打包说明

此原型可使用PyInstaller打包为Windows EXE文件：

```bash
pip install pyinstaller
pyinstaller --windowed --name MuOuCover --icon=logo.ico --add-data "models;models" app.py
```

## 命令行处理工具

项目提供了命令行处理工具 `cli_processor.py`，可以直接在命令行中进行音频处理操作。

### 功能

- 列出可用的模型
- 处理音频文件（音色转换）
- 支持多种音高提取器和处理参数

### 使用方法

#### 列出可用模型

```bash
python cli_processor.py list-models
```

#### 处理音频文件

```bash
python cli_processor.py process -i <输入音频路径> -o <输出音频路径> -m <模型路径> [可选参数]
```

#### 参数说明

- `-i`, `--input`: 输入音频文件或目录路径
- `-o`, `--output`: 输出音频文件或目录路径
- `-m`, `--model`: 模型路径
- `-c`, `--config`: 配置文件路径（可选）
- `-s`, `--spk-id`: 说话人ID (默认: 1)
- `-mix`, `--spk-mix-dict`: 说话人混合字典 (默认: "None")
- `-k`, `--key`: 音高变化 (默认: 0)
- `-f`, `--formant-shift-key`: 共振峰变化 (默认: 0)
- `-v`, `--vocal-register-shift-key`: 声区变化 (默认: 0)
- `-pe`, `--pitch-extractor`: 音高提取器 (默认: "rmvpe")
- `-fmin`, `--f0-min`: 最低频率 (默认: 50)
- `-fmax`, `--f0-max`: 最高频率 (默认: 1100)
- `-th`, `--threhold`: 阈值 (默认: -60)
- `-step`, `--infer-step`: 推理步骤 (默认: "auto")
- `-meth`, `--method`: 采样方法 (默认: "auto")
- `-ts`, `--t-start`: 开始时间 (默认: "auto")
- `-d`, `--device`: 设备 (默认: "cuda"如果可用，否则"cpu")
- `-fmt`, `--output-format`: 输出音频格式 (默认: "wav")
- `--dry-mode`: 使用干声模式（直接处理输入）
- `--full-mode`: 使用完整模式（先分离再处理）

### 示例

```bash
# 干声模式（直接处理单个音频文件）
python cli_processor.py -i "input.wav" -o "output.wav" -m "models/your_model.pt" -c "models/your_model.yaml" -k 2 -f 1 -v 0 -pe rmvpe --dry-mode

# 完整模式（先分离音频再处理）
python cli_processor.py -i "input.mp3" -o "output_dir" -m "models/your_model.pt" -k 0 -fmt mp3 --full-mode

# 批量处理整个文件夹
python cli_processor.py -i "input_folder" -o "output_folder" -m "models/your_model.pt" -d cpu -s 2 --full-mode
```

## API服务器

项目提供了API服务器 `api_server.py`，可以通过HTTP请求进行音频处理操作。

### 功能

- 获取可用模型列表
- 上传并处理音频文件
- 下载处理后的音频文件

### 启动服务器

```bash
python api_server.py [--host 主机地址] [--port 端口] [--debug]
```

参数说明：
- `--host`: 服务器主机地址 (默认: 127.0.0.1)
- `--port`: 服务器端口 (默认: 5000)
- `--debug`: 启用调试模式

### API接口

#### 1. 获取可用模型列表

```
GET /api/models
```

响应示例：
```json
{
  "status": "success",
  "models": [
    {
      "name": "model_name",
      "path": "models/model_name.pt",
      "has_config": true,
      "config_path": "models/model_name.yaml"
    }
  ]
}
```

#### 2. 处理音频文件

```
POST /api/process
```

请求参数 (multipart/form-data)：
- `file`: 音频文件 (必需)
- `model_path`: 模型路径 (必需)
- `config_path`: 配置文件路径 (可选)
- `device`: 设备 (可选)
- `spk_id`: 说话人ID (可选)
- `spk_mix_dict`: 混合说话人字典 (可选)
- `key`: 音高变化 (可选)
- `formant_shift_key`: 共振峰变化 (可选)
- `vocal_register_shift_key`: 声区变化 (可选)
- `pitch_extractor`: 音高提取器类型 (可选)
- `f0_min`: 最小f0 (可选)
- `f0_max`: 最大f0 (可选)
- `threhold`: 响应阈值 (可选)
- `infer_step`: 采样步骤 (可选)
- `method`: 采样方法 (可选)
- `t_start`: t_start参数 (可选)

响应示例：
```json
{
  "status": "success",
  "message": "音频处理成功",
  "download_url": "/api/download/processed_filename.wav"
}
```

#### 3. 下载处理后的音频文件

```
GET /api/download/<filename>
```

### 示例 (使用curl)

```bash
# 获取可用模型列表
curl http://localhost:5000/api/models

# 处理音频文件
curl -F "file=@input.wav" -F "model_path=models/my_model.pt" -F "key=2" http://localhost:5000/api/process

# 下载处理后的音频文件
curl -o processed.wav http://localhost:5000/api/download/processed_input.wav
```

# DDSP 6.3
DDSP 6.3是一个基于DDSP技术的音频处理工具，用于声音转换和音色修改。

## 功能特点
- 声音转换：转换输入音频的音色
- 音高调整：调整音频的音高
- 共振峰调整：调整音频的共振峰
- 声区变化：调整声音的声区
- 音频分离：将混合音频分离为人声、伴奏和和声（如果存在）

## 文件结构
- `app.py` - 主图形界面应用程序
- `cli_processor.py` - 命令行处理工具，支持干声模式和完整模式
- `api_server.py` - REST API服务器
- `main_reflow.py` - 声音转换核心脚本
- `msst/` - 音频分离组件

## 安装
1. 克隆代码库
2. 安装依赖包

## 使用方法

### 1. 图形界面
```bash
python app.py
```

### 2. 命令行处理（cli_processor.py）
命令行处理器支持两种模式：

#### a) 干声模式（直接处理输入音频）
```bash
python cli_processor.py -i "input.wav" -o "output.wav" -m "models/your_model.pt" --dry-mode
```

#### b) 完整模式（先分离再处理）
```bash
python cli_processor.py -i "input.wav" -o "output_dir" -m "models/your_model.pt" --full-mode
```

#### c) 批处理模式（处理整个文件夹）
```bash
python cli_processor.py -i "input_folder" -o "output_folder" -m "models/your_model.pt" --full-mode
```

#### 参数说明
- `-i`, `--input`: 输入音频文件或目录路径
- `-o`, `--output`: 输出音频文件或目录路径
- `-m`, `--model`: 模型路径
- `-c`, `--config`: 配置文件路径（可选）
- `-s`, `--spk-id`: 说话人ID (默认: 1)
- `-mix`, `--spk-mix-dict`: 说话人混合字典 (默认: "None")
- `-k`, `--key`: 音高变化 (默认: 0)
- `-f`, `--formant-shift-key`: 共振峰变化 (默认: 0)
- `-v`, `--vocal-register-shift-key`: 声区变化 (默认: 0)
- `-pe`, `--pitch-extractor`: 音高提取器 (默认: "rmvpe")
- `-fmin`, `--f0-min`: 最低频率 (默认: 50)
- `-fmax`, `--f0-max`: 最高频率 (默认: 1100)
- `-th`, `--threhold`: 阈值 (默认: -60)
- `-step`, `--infer-step`: 推理步骤 (默认: "auto")
- `-meth`, `--method`: 采样方法 (默认: "auto")
- `-ts`, `--t-start`: 开始时间 (默认: "auto")
- `-d`, `--device`: 设备 (默认: "cuda"如果可用，否则"cpu")
- `-fmt`, `--output-format`: 输出音频格式 (默认: "wav")
- `--dry-mode`: 使用干声模式（直接处理输入）
- `--full-mode`: 使用完整模式（先分离再处理）

### 3. API服务器（api_server.py）
启动API服务器：
```bash
python api_server.py --host 0.0.0.0 --port 5000
```

#### API端点
- `/api/models`: 获取可用模型列表
- `/api/process`: 处理音频文件
- `/api/separate`: 仅进行音频分离
- `/api/download/<path>`: 下载处理后的文件

## 注意事项

### 关于路径问题
cli_processor.py 和 api_server.py 在使用完整模式进行音频分离时，依赖于msst目录中的preset_infer_cli.py脚本。为了解决路径问题，两个脚本都会在调用分离脚本前先切换工作目录到msst目录。这意味着：

1. msst目录必须位于与cli_processor.py或api_server.py相同的目录中
2. msst目录结构需要正确，特别是:
   - scripts/preset_infer_cli.py
   - preset.json
   - tools/i18n/locale/en_US.json 等国际化文件

当在其他机器上使用这些脚本时，请确保维持这种目录结构关系，否则可能会出现路径错误。

## 问题排查

如果遇到类似 "FileNotFoundError: [Errno 2] No such file or directory: 'tools/i18n/locale/en_US.json'" 的错误，这通常表示工作目录不正确。请检查msst目录结构是否完整。
