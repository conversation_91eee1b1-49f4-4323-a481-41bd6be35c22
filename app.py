import sys
import os
import platform
import logging
import subprocess
import threading
import requests
import time
import json
import tempfile
import shutil
import io
import urllib.parse
from pathlib import Path

# 导入激活相关模块
import activation_utils

# 检查环境依赖
try:
    from PIL import Image
    print("成功导入PIL.Image模块")
except ImportError as e:
    print(f"PIL导入错误: {str(e)}")
    print("正在尝试修复PIL导入问题...")

    # 设置环境变量，使matplotlib使用非交互式后端
    os.environ['MPLBACKEND'] = 'Agg'
    pass

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QComboBox, QSlider, QLineEdit, QCheckBox, QScrollArea,
    QFileDialog, QSplitter, QFrame, QSizePolicy, QSpacerItem, QMessageBox,
    QDialog, QMenu, QAction
)
from PyQt5.QtCore import Qt, QTimer, QSize, pyqtSignal, QUrl, QMimeData, QByteArray, QBuffer, QPointF, QRect, QPoint
from PyQt5.QtGui import QFont, QPalette, QColor, QDragEnterEvent, QDropEvent, QPixmap, QImage, QPainter, QPen, QPainterPath, QIcon
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
import pyqtgraph as pg
import numpy as np
from pydub import AudioSegment

# 设置FFmpeg路径
FFMPEG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ffmpeg", "bin", "ffmpeg.exe")
if not os.path.exists(FFMPEG_PATH):
    # 如果在当前目录下找不到，则尝试使用系统路径
    FFMPEG_PATH = "ffmpeg"

# --- 自定义混响函数 ---
def add_reverb(audio, room_size=0.3, damping=0.1, wet_level=0.2, dry_level=0.9):
    # 将AudioSegment转换为numpy数组并处理
    y = np.array(audio.get_array_of_samples()).astype(np.float32)
    
    # 如果是立体声，提取左右声道
    is_stereo = audio.channels == 2
    if is_stereo:
        y = y.reshape((-1, 2))
    
    # 创建延迟和衰减参数
    delay_samples = int(room_size * audio.frame_rate * 0.2)  # 最大延迟0.2秒
    decay = 1.0 - damping  # 衰减因子
    
    # 创建延迟信号
    y_reverb = np.zeros_like(y)
    for i in range(1, 5):  # 创建多个延迟，模拟多次反射
        delay = int(delay_samples * i)
        if delay >= len(y): continue
        amplitude = decay ** i
        if is_stereo:
            y_reverb[delay:, 0] += y[:-delay, 0] * amplitude * wet_level
            y_reverb[delay:, 1] += y[:-delay, 1] * amplitude * wet_level
        else:
            y_reverb[delay:] += y[:-delay] * amplitude * wet_level
    
    # 混合原始信号和混响信号并裁剪
    y_out = np.clip(y * dry_level + y_reverb, -np.iinfo(np.int16).max, np.iinfo(np.int16).max).astype(np.int16)
    
    # 创建新的AudioSegment
    return audio._spawn(y_out)

# --- 日志配置 ---
def setup_logging():
    """设置日志记录，移除文件输出，仅保留控制台输出"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    return logging.getLogger('MuOuApp')

# --- 常量和配置 ---
WINDOW_TITLE = "木偶AI翻唱"
WINDOW_WIDTH = 1250 # 稍微加宽以适应新样式
WINDOW_HEIGHT = 1000 # 稍微加高

# 更新配色方案，参考现代深色UI设计 (基于提供的图片)
DEFAULT_THEME_BACKGROUND = "#161B22"      # 非常深的背景 (类似 GitHub Dark)
DEFAULT_THEME_SECONDARY_BG = "#1C2128"    # 次级背景/元素背景
DEFAULT_THEME_CARD_BG = "#21262D"         # 卡片/区块背景
DEFAULT_THEME_FOREGROUND = "#E6EDF3"      # 主要文本颜色 (近白色)
DEFAULT_THEME_SECONDARY_TEXT = "#8B949E"  # 次级/较暗文本颜色
DEFAULT_THEME_ACCENT = "#58A6FF"          # 蓝色强调色 (明亮蓝)
DEFAULT_THEME_ACCENT_HOVER = "#79C0FF"    # 悬停时的强调色 (更亮蓝)
DEFAULT_THEME_ACCENT_PRESSED = "#388BFD"  # 按下时的强调色 (稍深蓝)
DEFAULT_THEME_BORDER = "#30363D"          # 边框颜色
DEFAULT_THEME_WAVEFORM_BG = "#282E33"     # 波形图背景色 (可以与卡片背景相似或略有不同)
DEFAULT_THEME_SUCCESS = "#56D364"         # 成功颜色 (绿色)
DEFAULT_THEME_WARNING = "#FFA657"         # 警告颜色 (橙色)
DEFAULT_THEME_ERROR = "#F85149"           # 错误颜色 (红色)
# DEFAULT_THEME_SLIDER_HANDLE_BG = "#E6EDF3" # 旧的滑块手柄背景 (白色)
DEFAULT_THEME_SLIDER_HANDLE_BG = DEFAULT_THEME_ACCENT # 滑块手柄背景 (强调色) - 根据用户反馈修改
DEFAULT_THEME_SLIDER_TRACK_FILLED = DEFAULT_THEME_ACCENT # 滑块已填充部分颜色
DEFAULT_THEME_SLIDER_TRACK_EMPTY = DEFAULT_THEME_SECONDARY_BG # 滑块未填充部分颜色


FONT_FAMILY = "Microsoft YaHei UI" # 根据系统和偏好选择合适的字体
PLACEHOLDER_ICON_FONT_SIZE = 18 # 占位符图标字体大小
CONTROL_FONT_SIZE = 10          # 控件字体大小
LABEL_FONT_SIZE = 11            # 标签字体大小
BOLD_FONT_SIZE = 12             # 加粗字体大小
TITLE_FONT_SIZE = 14            # 标题字体大小
SECTION_HEADER_FONT_SIZE = 16   # 区块标题字体大小

if not os.path.exists("models"):
    os.makedirs("models")
    for name in ["voice_model_alpha", "singer_beta", "another_model"]:
        with open(os.path.join("models", f"{name}.pt"), "w") as f: f.write("dummy pt")
        if name != "another_model":
            with open(os.path.join("models", f"{name}.yaml"), "w") as f: f.write("dummy yaml")
    with open("models/config_only.yaml", "w") as f: f.write("dummy yaml")

class ClickableFrame(QFrame):
    clicked = pyqtSignal()
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setCursor(Qt.PointingHandCursor)
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

# --- 使用pyqtgraph绘制波形功能 ---
class WaveformPlot(pg.PlotWidget):

    positionClicked = pyqtSignal(float)  # 信号：当波形被点击时发出，参数为相对位置(0-1)

    def __init__(self, parent=None):
        # 配置pyqtgraph样式
        pg.setConfigOptions(antialias=True)

        # 设置自定义主题
        self.custom_theme = {
            'background': str(DEFAULT_THEME_WAVEFORM_BG or "#282E33"),
            'foreground': str(DEFAULT_THEME_FOREGROUND or "#E6EDF3"),
            'accent': str(DEFAULT_THEME_ACCENT or "#58A6FF"),
            'played': str(DEFAULT_THEME_ACCENT or "#58A6FF"),  # 已播放部分的颜色
            'unplayed': "#8B949E"  # 未播放部分的颜色（灰色）
        }

        # 创建绘图组件
        super().__init__(parent=parent, background=self.custom_theme['background'])

        # 设置绘图区域样式 - 固定高度为50像素（稍微减小高度）
        self.setFixedHeight(50)  # 减小高度，使界面更紧凑
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet(f"border: 1px solid {DEFAULT_THEME_BORDER}; border-radius: 8px;")

        # 配置绘图属性
        self.plotItem.showGrid(False, False)
        self.plotItem.hideAxis('bottom')
        self.plotItem.hideAxis('left')
        self.plotItem.setMenuEnabled(False)

        # 禁用默认的交互功能
        self.setMouseEnabled(x=False, y=False)
        self.setMenuEnabled(False)

        # 数据存储
        self.audio_samples = None
        self.sample_rate = None
        self.duration = 0
        self.position_line = None
        self.current_position = 0  # 当前播放位置（0-1之间的比例）
        self.waveform_item = None  # 单一波形项目
        self.played_region = None  # 已播放区域

        # 添加鼠标点击事件
        self.scene().sigMouseClicked.connect(self.on_mouse_clicked)

        # 添加用于显示错误或提示信息的标签
        self.info_label = QLabel()
        self.info_label.setStyleSheet(f"color: {DEFAULT_THEME_SECONDARY_TEXT}; background-color: transparent;")
        self.info_label.setFont(QFont(FONT_FAMILY, LABEL_FONT_SIZE))
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.hide()  # 初始隐藏

        # 使用布局添加标签
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.info_label)
        layout.setAlignment(Qt.AlignCenter)

        self.max_points = 500  # 降低采样点数，减少资源消耗
        self._waveform_cache = {}  # 缓存波形数据

    def load_audio(self, filepath):
        """加载音频文件并绘制波形"""
        try:
            # 隐藏信息标签并清除当前波形
            self.info_label.hide()
            self.clear()

            # 加载音频文件
            audio = AudioSegment.from_file(filepath)
            self.sample_rate = audio.frame_rate
            self.duration = len(audio) / 1000.0  # 转换为秒

            # 获取音频采样并转为单声道
            samples = np.array(audio.get_array_of_samples())
            if audio.channels == 2:
                samples = samples.reshape((-1, 2)).mean(axis=1)

            # 高效降采样 - 使用向量化操作提高性能
            max_points = self.max_points
            if len(samples) > max_points:
                # 计算降采样比例
                ratio = len(samples) // max_points
                # 使用向量化操作进行降采样
                samples = self._efficient_downsample(samples, ratio)

            # 标准化波形振幅
            if len(samples) > 0 and np.max(np.abs(samples)) > 0:
                samples = samples / np.max(np.abs(samples)) * 0.8

            # 创建对称波形 - 使用向量化操作
            mirrored_samples = np.zeros(len(samples) * 2)
            mirrored_samples[0::2] = samples  # 正值
            mirrored_samples[1::2] = -samples  # 负值镜像
            self.audio_samples = mirrored_samples

            # 创建时间轴
            time_axis = np.linspace(0, self.duration, len(mirrored_samples))

            # 绘制波形 - 使用安全的默认颜色
            unplayed_color = self._get_safe_color(self.custom_theme.get('unplayed'), "#8B949E")
            pen = pg.mkPen(color=unplayed_color, width=1.2)
            self.waveform_item = self.plot(time_axis, mirrored_samples, pen=pen)

            # 创建已播放区域
            played_color = self._get_safe_color(self.custom_theme.get('played'), "#58A6FF")
            brush_color = QColor(played_color)
            brush_color.setAlpha(80)  # 设置透明度
            self.played_region = pg.LinearRegionItem(
                [0, 0],  # 初始范围为0
                movable=False,
                brush=pg.mkBrush(color=brush_color),
                pen=pg.mkPen(color=played_color, width=0)
            )
            self.addItem(self.played_region)

            # 添加当前位置指示线
            self.position_line = pg.InfiniteLine(
                pos=0,
                angle=90,
                pen=pg.mkPen(color='#FFFFFF', width=1.2)
            )
            self.addItem(self.position_line)

            # 初始更新位置
            self.update_position(0)
            return True
        except Exception as e:
            logging.error(f"波形加载失败: {str(e)}")
            self.show_info_message(f"[波形加载失败: {str(e)[:30]}...]")
            return False
            
    def _efficient_downsample(self, samples, ratio):
        """高效的波形降采样方法"""
        # 将样本分组
        n_chunks = len(samples) // ratio
        if n_chunks == 0:
            return samples
            
        # 重塑数组以便进行向量化操作
        reshaped = samples[:n_chunks * ratio].reshape((n_chunks, ratio))
        
        # 计算每组的RMS值
        square_means = np.mean(np.square(reshaped), axis=1)
        # 对于零或负值，使用绝对值的平均值
        mask = square_means <= 0
        result = np.sqrt(square_means)
        if np.any(mask):
            result[mask] = np.mean(np.abs(reshaped[mask]), axis=1)
            
        return result
        
    def _get_safe_color(self, color, default_color):
        """安全地获取颜色值，确保返回有效的颜色字符串"""
        if not color or not isinstance(color, str):
            return default_color
        return color

    def update_position(self, position):
        """更新当前播放位置指示线和波形颜色"""
        self.current_position = position  # 存储当前位置（0-1之间）

        # 更新位置线
        if self.position_line and self.duration > 0:
            self.position_line.setPos(position * self.duration)

        # 更新已播放区域
        if self.played_region and self.duration > 0:
            self.played_region.setRegion([0, position * self.duration])

    def on_mouse_clicked(self, event):
        """处理鼠标点击事件，发出位置信号"""
        if self.sceneBoundingRect().contains(event.scenePos()):
            # 计算点击位置相对于波形的位置
            mouse_point = self.plotItem.vb.mapSceneToView(event.scenePos())
            x_pos = mouse_point.x()

            # 计算相对位置（0-1之间）
            if self.duration > 0:
                relative_pos = max(0, min(1, x_pos / self.duration))
                self.current_position = relative_pos
                self.update_position(relative_pos)
                self.positionClicked.emit(relative_pos)

    def show_info_message(self, message):
        """显示信息或错误消息"""
        self.clear()  # 清除现有的绘图项目
        self.info_label.setText(message)
        self.info_label.show()

# 修改DetailedAudioPlayerWidget类，添加波形显示和实际播放功能
class DetailedAudioPlayerWidget(QFrame):
    def __init__(self, parent=None, show_title=True, show_metadata=False, play_callback=None, remix_callback=None):
        super().__init__(parent)
        self.play_callback = play_callback
        self.remix_callback = remix_callback  # 重新混音回调函数
        self.audio_data = None

        self.setObjectName("DetailedAudioPlayerWidget")

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 10, 20, 10)  # 减小上下边距
        main_layout.setSpacing(10)  # 减小间距

        content_frame = QFrame()
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(0,0,0,0)
        content_layout.setSpacing(8)  # 减小间距
        main_layout.addWidget(content_frame)

        if show_title:
            self.title_label = QLabel("选择一个音轨进行播放")
            font = QFont(FONT_FAMILY, TITLE_FONT_SIZE, QFont.Bold)
            self.title_label.setFont(font)
            self.title_label.setAlignment(Qt.AlignLeft)
            self.title_label.setStyleSheet(f"color: {DEFAULT_THEME_FOREGROUND}; margin-bottom: 5px;")
            content_layout.addWidget(self.title_label)

        player_waveform_frame = QFrame()
        player_waveform_layout = QHBoxLayout(player_waveform_frame)
        player_waveform_layout.setContentsMargins(0,0,0,0)
        player_waveform_layout.setSpacing(15)  # 减小播放按钮和波形图之间的间距
        content_layout.addWidget(player_waveform_frame)

        # 播放按钮
        self.play_button = QPushButton("▶")
        font_play = QFont(FONT_FAMILY, 20, QFont.Bold)  # 减小字体
        self.play_button.setFont(font_play)
        self.play_button.setFixedSize(50, 50)  # 减小按钮尺寸
        self.play_button.setObjectName("CircularPlayButton")
        self.play_button.clicked.connect(self._on_play_pressed)
        player_waveform_layout.addWidget(self.play_button)

        # 添加重新混音按钮（仅在最终混音音轨显示）
        self.remix_button = QPushButton("🔄")
        self.remix_button.setFont(font_play)
        self.remix_button.setFixedSize(50, 50)
        self.remix_button.setObjectName("CircularPlayButton")
        self.remix_button.setToolTip("重新混音")
        self.remix_button.clicked.connect(self._on_remix_pressed)
        self.remix_button.hide()  # 默认隐藏
        player_waveform_layout.addWidget(self.remix_button)

        # 波形和控制区域
        waveform_controls_frame = QFrame()
        waveform_controls_layout = QVBoxLayout(waveform_controls_frame)
        waveform_controls_layout.setContentsMargins(0,0,0,0)
        waveform_controls_layout.setSpacing(3)  # 减小间距

        # 使用pyqtgraph的波形显示组件
        self.waveform_view = WaveformPlot()
        self.waveform_view.setMinimumHeight(40)  # 减小波形图高度
        self.waveform_view.positionClicked.connect(self._on_waveform_clicked)
        waveform_controls_layout.addWidget(self.waveform_view)

        # 时间进度显示
        self.time_layout = QHBoxLayout()
        self.time_layout.setContentsMargins(0,0,0,0)

        self.current_time_label = QLabel("0:00")
        self.current_time_label.setStyleSheet(f"color: {DEFAULT_THEME_SECONDARY_TEXT};")
        self.current_time_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        self.current_time_label.setAlignment(Qt.AlignLeft)

        self.duration_label = QLabel("0:00")
        self.duration_label.setStyleSheet(f"color: {DEFAULT_THEME_SECONDARY_TEXT};")
        self.duration_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        self.duration_label.setAlignment(Qt.AlignRight)

        # 添加音量按钮
        self.volume_button = QPushButton("🔊")
        self.volume_button.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE + 2))
        self.volume_button.setObjectName("VolumeButton")
        self.volume_button.setFixedSize(32, 32)
        self.volume_button.clicked.connect(self._toggle_volume_slider)
        self.volume_button.setCursor(Qt.PointingHandCursor)

        self.time_layout.addWidget(self.current_time_label)
        self.time_layout.addStretch()
        self.time_layout.addWidget(self.volume_button)
        self.time_layout.addWidget(self.duration_label)

        waveform_controls_layout.addLayout(self.time_layout)
        player_waveform_layout.addWidget(waveform_controls_frame, 1)

        # 创建音量弹出窗口
        self.volume_popup = QFrame(self)
        self.volume_popup.setObjectName("VolumePopup")
        self.volume_popup.setFixedSize(40, 150)
        self.volume_popup.setStyleSheet(f"""
            QFrame#VolumePopup {{
                background-color: {DEFAULT_THEME_CARD_BG};
                border: 1px solid {DEFAULT_THEME_BORDER};
                border-radius: 6px;
            }}
        """)
        volume_popup_layout = QVBoxLayout(self.volume_popup)
        volume_popup_layout.setContentsMargins(5, 10, 5, 10)
        volume_popup_layout.setSpacing(5)

        # 添加音量图标
        self.volume_icon = QLabel("🔊")
        self.volume_icon.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE + 2))
        self.volume_icon.setStyleSheet(f"color: {DEFAULT_THEME_FOREGROUND};")
        self.volume_icon.setAlignment(Qt.AlignCenter)
        volume_popup_layout.addWidget(self.volume_icon)

        # 添加垂直音量滑杆
        self.volume_slider = QSlider(Qt.Vertical)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setFixedHeight(100)
        self.volume_slider.valueChanged.connect(self._on_volume_changed)
        volume_popup_layout.addWidget(self.volume_slider, 1, Qt.AlignCenter)

        # 隐藏音量弹窗
        self.volume_popup.hide()

        if show_metadata:
            self.metadata_label = QLabel("处理信息: N/A")
            font_meta = QFont(FONT_FAMILY, CONTROL_FONT_SIZE)
            self.metadata_label.setFont(font_meta)
            self.metadata_label.setAlignment(Qt.AlignLeft)
            self.metadata_label.setStyleSheet(f"color: {DEFAULT_THEME_SECONDARY_TEXT}; padding-top: 8px;")
            content_layout.addWidget(self.metadata_label)

        self.setLayout(main_layout)

        # 初始化媒体播放器
        self.media_player = QMediaPlayer()
        self.media_player.setVolume(70)  # 初始音量设为70%

        # 连接媒体播放器信号
        self.media_player.stateChanged.connect(self._on_playback_state_changed)
        self.media_player.positionChanged.connect(self._on_position_changed)
        self.media_player.durationChanged.connect(self._on_duration_changed)

        # 设置更新波形位置的计时器
        self.position_timer = QTimer()
        self.position_timer.setInterval(50)  # 每50毫秒更新一次
        self.position_timer.timeout.connect(self._update_position)

        # 添加事件过滤器，用于检测点击事件
        self.installEventFilter(self)

    def _on_remix_pressed(self):
        """重新混音按钮点击处理"""
        if self.remix_callback and self.audio_data:
            self.remix_callback(self.audio_data)

    def load_audio(self, audio_data):
        """加载音频并准备播放"""
        self.audio_data = audio_data
        if hasattr(self, 'title_label'):
            self.title_label.setText(audio_data.get('title', "无标题"))

        # 根据音轨类型显示或隐藏重新混音按钮
        if audio_data.get('id') == 'mix' and self.remix_callback is not None:
            self.remix_button.show()
        else:
            self.remix_button.hide()

        # 更新波形显示
        filepath = audio_data.get('filepath')
        if filepath and os.path.exists(filepath):
            try:
                # 停止当前播放
                self.media_player.stop()

                # 加载新的媒体文件
                self.media_player.setMedia(QMediaContent(QUrl.fromLocalFile(filepath)))

                # 加载波形
                waveform_loaded = self.waveform_view.load_audio(filepath)

                if not waveform_loaded:
                    # 使用WaveformPlot类的方法显示错误信息
                    self.waveform_view.show_info_message("[波形加载失败]")

            except Exception as e:
                # 发生任何错误，显示默认文本
                logging.error(f"音频加载异常: {str(e)}")
                self.waveform_view.clear()
                self.waveform_view.show_info_message(f"[波形显示错误: {str(e)[:30]}...]")
        else:
            # 如果文件不存在，清除波形显示
            self.waveform_view.clear()
            self.media_player.setMedia(QMediaContent())

            # 显示占位符消息
            placeholder_text = audio_data.get('waveform_text', "[音频波形]")
            self.waveform_view.show_info_message(placeholder_text)

        if hasattr(self, 'metadata_label'):
            self.metadata_label.setText(audio_data.get('metadata_text', "处理信息: N/A"))

        self.play_button.setEnabled(True)
        self.play_button.setText("▶")  # 重置播放按钮

        # 重置时间标签
        self.current_time_label.setText("0:00")
        self.duration_label.setText("0:00")

    def eventFilter(self, obj, event):
        # 处理点击事件，如果点击了播放器界面上非音量弹窗区域，则隐藏音量弹窗
        if event.type() == event.MouseButtonPress:
            if self.volume_popup.isVisible():
                # 获取音量按钮的全局位置
                button_geo = QRect(self.volume_button.mapToGlobal(QPoint(0, 0)),
                                   self.volume_button.size())
                # 获取弹窗的全局位置
                popup_geo = QRect(self.volume_popup.mapToGlobal(QPoint(0, 0)),
                                 self.volume_popup.size())

                # 检查点击是否在音量按钮或音量弹窗内
                click_pos = event.globalPos()
                if not (button_geo.contains(click_pos) or popup_geo.contains(click_pos)):
                    self.volume_popup.hide()
        return super().eventFilter(obj, event)

    def _toggle_volume_slider(self):
        """切换音量滑块的显示状态"""
        if self.volume_popup.isVisible():
            self.volume_popup.hide()
        else:
            # 计算弹出位置：音量按钮上方
            button_pos = self.volume_button.mapToGlobal(QPoint(0, 0))
            popup_pos = self.mapFromGlobal(button_pos)
            popup_pos.setX(popup_pos.x() - self.volume_popup.width() // 2 + self.volume_button.width() // 2)
            popup_pos.setY(popup_pos.y() - self.volume_popup.height())
            self.volume_popup.move(popup_pos)
            self.volume_popup.show()

    def _on_play_pressed(self):
        """播放/暂停按钮点击处理"""
        if self.play_callback and self.audio_data:
            self.play_callback(self.audio_data)
            return

        filepath = self.audio_data.get('filepath', None) if self.audio_data else None
        if not filepath or not os.path.exists(filepath):
            return

        # 使用内置媒体播放器控制播放
        if self.media_player.state() == QMediaPlayer.PlayingState:
            self.media_player.pause()
        else:
            self.media_player.play()

    def _on_volume_changed(self, value):
        """处理音量滑块变化"""
        self.media_player.setVolume(value)

        # 更新音量图标
        if value == 0:
            self.volume_icon.setText("🔇")
        elif value < 30:
            self.volume_icon.setText("🔈")
        elif value < 70:
            self.volume_icon.setText("🔉")
        else:
            self.volume_icon.setText("🔊")

    def _on_waveform_clicked(self, relative_position):
        """处理波形点击事件，跳转到对应位置"""
        if self.media_player.duration() > 0:
            position = int(relative_position * self.media_player.duration())
            self.media_player.setPosition(position)

    def _on_playback_state_changed(self, state):
        """处理播放状态变化"""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setText("■")
            self.position_timer.start()
        else:
            self.play_button.setText("▶")
            self.position_timer.stop()

    def _on_position_changed(self, position):
        """处理播放位置变化"""
        # 更新当前时间标签
        seconds = position // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        self.current_time_label.setText(f"{minutes}:{seconds:02d}")

    def _on_duration_changed(self, duration):
        """处理媒体时长变化"""
        # 更新总时长标签
        seconds = duration // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        self.duration_label.setText(f"{minutes}:{seconds:02d}")

    def _update_position(self):
        """更新波形位置指示器"""
        if self.media_player.duration() > 0:
            relative_pos = self.media_player.position() / self.media_player.duration()
            self.waveform_view.update_position(relative_pos)

class InputFileSelectionWidget(QFrame):
    def __init__(self, parent, app_instance):
        super().__init__(parent)
        self.app = app_instance
        self.logger = self.app.logger

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0,0,0,0)
        main_layout.setSpacing(18) # 增加间距

        self.select_area_frame = ClickableFrame()
        self.select_area_frame.setObjectName("SelectAreaFrame") # 用于 QSS
        # 样式在全局 QSS 中定义 (虚线边框等)
        select_area_layout = QVBoxLayout(self.select_area_frame)
        select_area_layout.setAlignment(Qt.AlignCenter)
        select_area_layout.setContentsMargins(40, 40, 40, 40) # 增加内边距
        select_area_layout.setSpacing(15)

        self.select_area_icon = QLabel("🎵")
        icon_font = QFont(FONT_FAMILY, 12) # 增大图标
        self.select_area_icon.setFont(icon_font)
        self.select_area_icon.setAlignment(Qt.AlignCenter)
        self.select_area_icon.setStyleSheet(f"color: {DEFAULT_THEME_ACCENT}; margin-bottom: 10px;")
        select_area_layout.addWidget(self.select_area_icon)

        self.select_area_text = QLabel("在此处选择或拖拽音频文件")
        text_font = QFont(FONT_FAMILY, LABEL_FONT_SIZE + 1, QFont.DemiBold) # 稍大一点的半粗体
        self.select_area_text.setFont(text_font)
        self.select_area_text.setAlignment(Qt.AlignCenter)
        self.select_area_text.setStyleSheet(f"color: {DEFAULT_THEME_SECONDARY_TEXT};")
        select_area_layout.addWidget(self.select_area_text)

        self.select_area_frame.clicked.connect(lambda: self.app.open_file_dialog(self.update_after_file_selection))
        self.select_area_frame.setMinimumHeight(150) # 增加最小高度
        # 启用拖放
        self.select_area_frame.setAcceptDrops(True)
        self.select_area_frame.dragEnterEvent = self.dragEnterEvent
        self.select_area_frame.dropEvent = self.dropEvent

        main_layout.addWidget(self.select_area_frame)

        self.selected_file_info_frame = QFrame()
        self.selected_file_info_frame.setObjectName("SelectedFileFrame") # 用于 QSS
        # 样式在全局 QSS 中定义
        selected_file_info_layout = QHBoxLayout(self.selected_file_info_frame)
        selected_file_info_layout.setContentsMargins(18, 15, 18, 15) # 调整内边距
        selected_file_info_layout.setSpacing(15)

        self.selected_file_label = QLabel("")
        self.selected_file_label.setFont(QFont(FONT_FAMILY, LABEL_FONT_SIZE))
        self.selected_file_label.setWordWrap(True)
        self.selected_file_label.setStyleSheet(f"color: {DEFAULT_THEME_FOREGROUND};")
        selected_file_info_layout.addWidget(self.selected_file_label, 1)

        self.clear_file_button = QPushButton()
        self.clear_file_button.setFixedSize(30, 30) # 调整大小为30x30像素
        self.clear_file_button.setObjectName("ClearFileButton") # 用于 QSS
        # 修改样式，使按钮为红色圆形，白色叉号
        self.clear_file_button.setStyleSheet(f"""
            QPushButton#ClearFileButton {{
                background-color: {DEFAULT_THEME_ERROR};
                color: white;
                border-radius: 15px;
                border: none;
                font-family: Arial;
                font-weight: bold;
                font-size: 16px;
            }}
            QPushButton#ClearFileButton:hover {{
                background-color: #FF6B64; /* 悬停时颜色略浅 */
            }}
            QPushButton#ClearFileButton::after {{
                content: "×";
            }}
        """)
        # 直接设置文本为×符号（比Unicode的❌更简洁清晰）
        self.clear_file_button.setText("×")
        self.clear_file_button.clicked.connect(self.clear_selected_file)
        selected_file_info_layout.addWidget(self.clear_file_button)

        main_layout.addWidget(self.selected_file_info_frame)
        self.selected_file_info_frame.hide()

        self.input_player_container = QFrame()
        self.input_player_layout = QVBoxLayout(self.input_player_container)
        self.input_player_layout.setContentsMargins(0,0,0,0)
        self.input_player_layout.setSpacing(15)
        main_layout.addWidget(self.input_player_container)
        self.input_audio_player = None

        self.setLayout(main_layout)

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            url = event.mimeData().urls()[0]
            if url.isLocalFile():
                file_path = url.toLocalFile()
                if file_path.lower().endswith(('.wav', '.mp3', '.ogg')):
                    self.logger.info(f"拖拽文件: {file_path}")
                    event.acceptProposedAction()
                    return
        event.ignore()

    def dropEvent(self, event):
        urls = event.mimeData().urls()
        if urls:
            file_path = urls[0].toLocalFile()
            self.logger.info(f"接收拖拽文件: {file_path}")
            self.update_after_file_selection(file_path)
            event.acceptProposedAction()

    def update_after_file_selection(self, filepath):
        if filepath:
            self.logger.info(f"选择文件: {filepath}")
            basename = os.path.basename(filepath)
            self.select_area_frame.hide()
            self.selected_file_label.setText(f"已选择: {basename}")
            self.selected_file_info_frame.show()
            if self.input_audio_player:
                self.input_player_layout.removeWidget(self.input_audio_player)
                self.input_audio_player.deleteLater()
            self.input_audio_player = DetailedAudioPlayerWidget(self.input_player_container, show_title=False)
            self.input_player_layout.addWidget(self.input_audio_player)
            self.input_audio_player.load_audio({
                'filepath': filepath, 'title': basename, 'waveform_text': f"[波形: {basename}]"
            })
            self.input_audio_player.play_button.setEnabled(bool(filepath))
            self.app.selected_audio_filepath = filepath

            # 更新处理按钮状态
            if hasattr(self.app, '_on_processing_mode_changed'):
                self.app._on_processing_mode_changed(self.app.processing_mode_combo.currentText())
        else:
            self.clear_selected_file()

    def clear_selected_file(self):
        self.logger.info("清除选择的文件")
        self.selected_file_info_frame.hide()
        if self.input_audio_player:
            self.input_player_layout.removeWidget(self.input_audio_player)
            self.input_audio_player.deleteLater()
            self.input_audio_player = None
        self.app.selected_audio_filepath = None
        self.select_area_frame.show()

        # 更新处理按钮状态
        if hasattr(self.app, '_on_processing_mode_changed'):
            self.app._on_processing_mode_changed(self.app.processing_mode_combo.currentText())

class OutputTrackEntryWidget(QFrame):
    def __init__(self, parent, track_data, play_action_callback, download_action_callback):
        super().__init__(parent)
        self.track_data = track_data
        self.play_action_callback = play_action_callback
        self.download_action_callback = download_action_callback

        self.setFixedHeight(85) # 增加高度
        self.setObjectName("OutputTrackEntry") # 用于 QSS
        # 样式在全局 QSS 中定义

        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(18)

        self.title_label = QLabel(track_data.get('title', 'N/A'))
        self.title_label.setFont(QFont(FONT_FAMILY, LABEL_FONT_SIZE + 1, QFont.Bold))
        self.title_label.setStyleSheet("color: white;") # 修改为白色文本，适应深色背景
        layout.addWidget(self.title_label, 1)

        icon_font = QFont(FONT_FAMILY, PLACEHOLDER_ICON_FONT_SIZE + 2) # 稍大图标

        self.play_button = QPushButton("▶")
        self.play_button.setFont(icon_font)
        self.play_button.setFixedSize(50, 50) # 调整按钮大小
        self.play_button.setObjectName("CircularAccentButton") # 用于 QSS
        self.play_button.clicked.connect(lambda: self.play_action_callback(self.track_data))
        # 调整播放按钮样式以适应深色背景
        self.play_button.setStyleSheet(f"""
            QPushButton#CircularAccentButton {{
                background-color: {DEFAULT_THEME_ACCENT};
                color: white;
                border-radius: 25px;
                border: none;
            }}
            QPushButton#CircularAccentButton:hover {{
                background-color: {DEFAULT_THEME_ACCENT_HOVER};
            }}
        """)
        layout.addWidget(self.play_button)

        self.download_button = QPushButton("⬇️")
        self.download_button.setFont(icon_font)
        self.download_button.setFixedSize(50, 50) # 调整按钮大小
        self.download_button.setObjectName("CircularSecondaryButton") # 用于 QSS
        self.download_button.clicked.connect(lambda: self.download_action_callback(self.track_data))
        # 调整下载按钮样式以适应深色背景
        self.download_button.setStyleSheet(f"""
            QPushButton#CircularSecondaryButton {{
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border-radius: 25px;
                border: 1px solid rgba(255, 255, 255, 0.5);
            }}
            QPushButton#CircularSecondaryButton:hover {{
                background-color: rgba(255, 255, 255, 0.3);
            }}
        """)
        layout.addWidget(self.download_button)

        self.setLayout(layout)

# 辅助函数：执行子进程并处理结果
def run_subprocess(cmd, hide_console=True, add_to_list=None):
    """
    执行子进程并返回结果

    Args:
        cmd: 命令列表
        hide_console: 是否隐藏控制台窗口
        add_to_list: 如果提供，将进程添加到此列表

    Returns:
        (returncode, stdout, stderr)
    """
    # 添加creationflags参数以隐藏控制台窗口
    creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' and hide_console else 0

    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        creationflags=creation_flags
    )

    # 如果提供了列表，将进程添加到列表中
    if add_to_list is not None:
        add_to_list.append(process)

    stdout, stderr = process.communicate()
    return process.returncode, stdout, stderr

class App(QMainWindow):
    def __init__(self):
        super().__init__()
        # 设置日志
        self.logger = setup_logging()
        self.logger.info("应用启动")

        # 配置AudioSegment的FFmpeg路径
        AudioSegment.converter = FFMPEG_PATH
        self.logger.info(f"设置FFmpeg路径: {FFMPEG_PATH}")

        self.setWindowTitle(WINDOW_TITLE)
        self.setGeometry(100, 100, WINDOW_WIDTH, WINDOW_HEIGHT)
        # 设置窗口图标
        icon_path = os.path.join("assets", "images", "title.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        # 主窗口背景色在全局 QSS 中设置

        # 检查软件激活状态
        self.check_activation()

        self.selected_audio_filepath = None
        self.processing = False
        self.processed_files = {}
        self.finished_songs = []  # 保存成品歌曲数据

        # 添加停止标志和子进程列表，用于处理停止功能
        self.should_stop = False
        self.subprocess_list = []

        # 创建UI元素
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(0,0,0,0) # 移除边距，让分割器占据全部

        self.splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(self.splitter)

        self.left_panel_container = QWidget()
        self.left_panel_container.setMinimumWidth(int(WINDOW_WIDTH * 0.38)) # 调整比例
        self.left_panel_container.setMaximumWidth(int(WINDOW_WIDTH * 0.55))

        self.right_panel_container = QWidget()

        self.splitter.addWidget(self.left_panel_container)
        self.splitter.addWidget(self.right_panel_container)
        self.splitter.setSizes([int(WINDOW_WIDTH * 0.4), int(WINDOW_WIDTH * 0.6)])
        self.splitter.setHandleWidth(2) # 使分割线更细

        self._create_left_panel_content(self.left_panel_container)
        self._create_right_panel_content(self.right_panel_container)
        self.apply_global_styles()

        # 初始化处理按钮状态
        self.process_button.setEnabled(False)
        self.process_button.setToolTip("请先选择音频文件")

        # 连接处理模式变更信号
        self.processing_mode_combo.currentTextChanged.connect(self._on_processing_mode_changed)

        # 连接分离模式变更信号
        if hasattr(self, 'separation_mode_combo'):
            self.separation_mode_combo.currentTextChanged.connect(self._on_separation_mode_changed)

        # 加载成品歌曲列表
        self.load_finished_songs()

    def _on_separation_mode_changed(self, mode):
        """分离模式变更时更新UI状态"""
        # 更新和声加入伴奏选项的状态
        if hasattr(self, 'harmony_to_accomp_check') and self.processing_mode_combo.currentText() == "完整模式":
            self.harmony_to_accomp_check.setEnabled(mode == "精细模式")
            if mode != "精细模式" and self.harmony_to_accomp_check.isChecked():
                self.harmony_to_accomp_check.setChecked(False)

    def _on_processing_mode_changed(self, mode):
        """处理模式变更时更新UI状态"""
        # 获取当前有无音频文件
        has_audio = self.selected_audio_filepath is not None

        # 根据模式和音频文件状态决定按钮状态
        button_enabled = has_audio
        tooltip = "" if has_audio else "请先选择音频文件"

        self.process_button.setEnabled(button_enabled)
        self.process_button.setToolTip(tooltip)

        # 干声模式下禁用混响和和声加入伴奏选项
        if mode == "完整模式":
            if hasattr(self, 'harmony_to_accomp_check'):
                self.harmony_to_accomp_check.setEnabled(True)
            if hasattr(self, 'reverb_enabled_check'):
                self.reverb_enabled_check.setEnabled(True)
                # if not self.reverb_enabled_check.isChecked():
                #     self.reverb_enabled_check.setChecked(True)  # 自动启用混响选项
        else:  # 干声模式
            # 禁用和声加入伴奏选项
            if hasattr(self, 'harmony_to_accomp_check'):
                self.harmony_to_accomp_check.setEnabled(False)
                if self.harmony_to_accomp_check.isChecked():
                    self.harmony_to_accomp_check.setChecked(False)

            # 禁用混响选项
            if hasattr(self, 'reverb_enabled_check'):
                self.reverb_enabled_check.setEnabled(False)
                if self.reverb_enabled_check.isChecked():
                    self.reverb_enabled_check.setChecked(False)

    def closeEvent(self, event):
        # 应用关闭事件处理
        self.logger.info("应用关闭")
        event.accept()

    def start_processing(self):
        """开始处理音频"""
        # 如果已经在处理中且是停止按钮，则停止处理
        if self.processing and self.process_button.text() == "■ 停止处理":
            self.stop_processing()
            return

        # 检查处理模式
        error_style = f"color: {DEFAULT_THEME_ERROR};"
        if not self.selected_audio_filepath:
            self.status_label.setText("错误: 请先选择一个音频文件。")
            self.status_label.setStyleSheet(error_style)
            return
        if self.model_combo.currentText() == "无模型文件":
            self.status_label.setText("错误: 请选择一个音色模型。")
            self.status_label.setStyleSheet(error_style)
            return
        if self.config_combo.currentText() == "无配置文件":
            self.status_label.setText("错误: 请选择一个配置文件。")
            self.status_label.setStyleSheet(error_style)
            return

        if self.processing:
            self.status_label.setText("错误: 正在处理中，请等待完成。")
            self.status_label.setStyleSheet(error_style)
            return

        self.status_label.setText(f"处理中: {os.path.basename(self.selected_audio_filepath)}...")
        self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_FOREGROUND};")
        self.processing = True

        # 改变按钮状态和文本
        self.process_button_original_text = self.process_button.text()  # 保存原始文本
        self.process_button.setText("■ 停止处理")

        # 创建处理线程
        self.logger.info(f"开始处理: 音频路径: {self.selected_audio_filepath}, 模型: {self.model_combo.currentText()}, 配置: {self.config_combo.currentText()}, 处理模式: {self.processing_mode_combo.currentText()}")
        self.processing_thread = threading.Thread(target=self.process_audio)
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def stop_processing(self):
        """停止处理音频"""
        self.logger.info("用户请求停止处理")

        # 使用QTimer确保UI更新在主线程中执行
        QTimer.singleShot(0, lambda: self.status_label.setText("正在停止处理..."))
        QTimer.singleShot(0, lambda: self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_WARNING};"))

        # 设置停止标志
        self.should_stop = True

        # 终止所有子进程
        killed_processes = 0
        for process in self.subprocess_list:
            try:
                if process and process.poll() is None:  # 如果进程还在运行
                    self.logger.info(f"正在终止子进程 PID: {process.pid}")
                    # 在Windows上，使用taskkill命令强制终止进程及其子进程
                    # 添加creationflags参数以隐藏控制台窗口
                    creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0

                    if sys.platform == 'win32':
                        subprocess.call(['taskkill', '/F', '/T', '/PID', str(process.pid)],
                                       stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL,
                                       creationflags=creation_flags)
                    else:
                        # 在其他平台上，使用terminate方法
                        process.terminate()
                        # 给进程一点时间来优雅地终止
                        time.sleep(0.1)
                        # 如果进程仍在运行，强制终止
                        if process.poll() is None:
                            process.kill()
                    killed_processes += 1
            except Exception as e:
                self.logger.error(f"终止子进程失败: {str(e)}")

        self.logger.info(f"已终止 {killed_processes} 个子进程")

        # 清空子进程列表
        self.subprocess_list = []

        # 停止API服务的进程（如果有）
        try:
            if hasattr(self, 'process') and self.process:
                # 添加creationflags参数以隐藏控制台窗口
                creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0

                if sys.platform == 'win32':
                    subprocess.call(['taskkill', '/F', '/T', '/PID', str(self.process.pid)],
                                   stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL,
                                   creationflags=creation_flags)
                else:
                    self.process.terminate()
                self.process = None
                self.logger.info("已终止API服务进程")
        except Exception as e:
            self.logger.error(f"停止API服务进程失败: {str(e)}")

        # 恢复UI状态
        self.processing = False

        # 使用QTimer确保UI更新在主线程中执行
        QTimer.singleShot(0, lambda: self.process_button.setText(
            self.process_button_original_text if hasattr(self, 'process_button_original_text') else "🚀 一键翻唱"))
        QTimer.singleShot(0, lambda: self.status_label.setText("处理已停止"))
        QTimer.singleShot(0, lambda: self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_WARNING};"))

        # 如果已有部分处理结果，可以显示
        if self.processed_files:
            QTimer.singleShot(0, self._display_processed_results)

    def apply_global_styles(self):
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {DEFAULT_THEME_BACKGROUND};
            }}
            QWidget {{
                font-family: "{FONT_FAMILY}";
                color: {DEFAULT_THEME_FOREGROUND};
            }}
            QLabel {{
                color: {DEFAULT_THEME_FOREGROUND};
                background-color: transparent; /* 确保标签背景透明 */
            }}
            QPushButton {{
                background-color: {DEFAULT_THEME_ACCENT};
                color: white; /* 按钮文字通常为白色 */
                border: none;
                padding: 10px 18px; /* 调整内边距 */
                border-radius: 8px; /* 统一圆角 */
                font-size: {CONTROL_FONT_SIZE + 1}pt; /* 稍大字体 */
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {DEFAULT_THEME_ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: {DEFAULT_THEME_ACCENT_PRESSED};
            }}
            QPushButton:disabled {{
                background-color: #484F58; /* 更深色的禁用背景 */
                color: #8B949E; /* 禁用文字颜色 */
            }}
            /* 主操作按钮的特定样式 */
            QPushButton#MainActionButton {{
                padding: 12px 20px;
                font-size: {BOLD_FONT_SIZE + 2}pt;
                border-radius: 10px;
            }}
            /* 圆形播放按钮 */
            QPushButton#CircularPlayButton {{
                background-color: {DEFAULT_THEME_ACCENT};
                color: white;
                border-radius: 40px; /* height / 2 */
                padding: 0px;
                font-size: 28pt; /* 根据按钮大小调整 */
            }}
            QPushButton#CircularPlayButton:hover {{ background-color: {DEFAULT_THEME_ACCENT_HOVER}; }}
            QPushButton#CircularPlayButton:pressed {{ background-color: {DEFAULT_THEME_ACCENT_PRESSED}; }}

            /* 圆形强调色按钮 (用于输出列表播放) */
            QPushButton#CircularAccentButton {{
                background-color: {DEFAULT_THEME_ACCENT};
                color: white;
                border-radius: 25px; /* height / 2 */
                padding: 0px;
            }}
            QPushButton#CircularAccentButton:hover {{ background-color: {DEFAULT_THEME_ACCENT_HOVER}; }}
            QPushButton#CircularAccentButton:pressed {{ background-color: {DEFAULT_THEME_ACCENT_PRESSED}; }}

            /* 圆形次要颜色按钮 (用于输出列表下载) */
            QPushButton#CircularSecondaryButton {{
                background-color: {DEFAULT_THEME_SECONDARY_BG};
                color: {DEFAULT_THEME_FOREGROUND};
                border-radius: 25px; /* height / 2 */
                border: 1px solid {DEFAULT_THEME_BORDER};
                padding: 0px;
            }}
            QPushButton#CircularSecondaryButton:hover {{ background-color: {DEFAULT_THEME_CARD_BG}; }}
            QPushButton#CircularSecondaryButton:pressed {{ background-color: {DEFAULT_THEME_BACKGROUND}; }}

            /* 清除文件按钮 - 改为注释掉这部分，因为已在局部样式中定义 */
            /*
            QPushButton#ClearFileButton {{
                background-color: {DEFAULT_THEME_SECONDARY_BG};
                color: {DEFAULT_THEME_SECONDARY_TEXT};
                border-radius: 19px;
                border: 1px solid {DEFAULT_THEME_BORDER};
            }}
            QPushButton#ClearFileButton:hover {{
                background-color: {DEFAULT_THEME_ERROR};
                color: white;
                border-color: {DEFAULT_THEME_ERROR};
            }}
            */

            /* 音量按钮 */
            QPushButton#VolumeButton {{
                background-color: transparent;
                color: {DEFAULT_THEME_SECONDARY_TEXT};
                border: none;
                border-radius: 16px;
                padding: 0px;
            }}
            QPushButton#VolumeButton:hover {{
                color: {DEFAULT_THEME_FOREGROUND};
                background-color: {DEFAULT_THEME_SECONDARY_BG};
            }}

            QComboBox {{
                background-color: {DEFAULT_THEME_SECONDARY_BG};
                color: {DEFAULT_THEME_FOREGROUND};
                border: 1px solid {DEFAULT_THEME_BORDER};
                padding: 9px 12px; /* 调整内边距 */
                border-radius: 8px;
                font-size: {CONTROL_FONT_SIZE}pt;
                min-height: 22px; /* 调整最小高度 */
            }}
            QComboBox::drop-down {{
                border: none;
                width: 22px;
                subcontrol-origin: padding;
                subcontrol-position: right center;
                image: url(PLACEHOLDER_FOR_DROPDOWN_ARROW_ICON); /* 推荐使用SVG图标 */
            }}
            QComboBox QAbstractItemView {{
                background-color: {DEFAULT_THEME_CARD_BG}; /* 下拉列表背景 */
                color: {DEFAULT_THEME_FOREGROUND};
                selection-background-color: {DEFAULT_THEME_ACCENT};
                border: 1px solid {DEFAULT_THEME_BORDER};
                border-radius: 8px;
                padding: 8px;
            }}
            QLineEdit {{
                background-color: {DEFAULT_THEME_SECONDARY_BG};
                color: {DEFAULT_THEME_FOREGROUND};
                border: 1px solid {DEFAULT_THEME_BORDER};
                padding: 9px 12px;
                border-radius: 8px;
                font-size: {CONTROL_FONT_SIZE}pt;
            }}
            QSlider::groove:horizontal {{
                border: none;
                height: 8px;
                background: {DEFAULT_THEME_SLIDER_TRACK_EMPTY};
                margin: 2px 0;
                border-radius: 4px;
            }}
            QSlider::sub-page:horizontal {{ /* 滑块已填充部分 */
                background: {DEFAULT_THEME_SLIDER_TRACK_FILLED};
                border: none;
                height: 8px;
                border-radius: 4px;
                margin: 2px 0;
            }}
            QSlider::add-page:horizontal {{ /* 滑块未填充部分 (在 sub-page 之后) */
                background: {DEFAULT_THEME_SLIDER_TRACK_EMPTY};
                border: none;
                height: 8px;
                border-radius: 4px;
                margin: 2px 0;
            }}
            QSlider::handle:horizontal {{
                background: {DEFAULT_THEME_SLIDER_HANDLE_BG}; /* 已更新为 DEFAULT_THEME_ACCENT */
                border: 2px solid {DEFAULT_THEME_ACCENT_PRESSED}; /* 手柄边框为深强调色 */
                width: 18px;
                height: 18px;
                margin: -7px 0px; /* 调整垂直对齐 */
                border-radius: 9px;
            }}
            QCheckBox {{
                spacing: 10px;
                font-size: {CONTROL_FONT_SIZE}pt;
                color: {DEFAULT_THEME_SECONDARY_TEXT}; /* 复选框文字颜色 */
            }}
            QCheckBox::indicator {{
                width: 20px;
                height: 20px;
                border: 1px solid {DEFAULT_THEME_BORDER};
                border-radius: 6px;
                background-color: {DEFAULT_THEME_SECONDARY_BG};
            }}
            QCheckBox::indicator:checked {{
                background-color: {DEFAULT_THEME_ACCENT};
                border-color: {DEFAULT_THEME_ACCENT};
                image: url(PLACEHOLDER_FOR_CHECKMARK_ICON); /* 推荐使用SVG打勾图标 */
            }}
            QCheckBox::indicator:hover {{
                border-color: {DEFAULT_THEME_ACCENT};
            }}
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background: {DEFAULT_THEME_BACKGROUND}; /* 滚动条背景 */
                width: 10px; /* 细一点的滚动条 */
                border-radius: 5px;
                margin: 0px;
            }}
            QScrollBar::handle:vertical {{
                background: {DEFAULT_THEME_BORDER}; /* 滚动条滑块颜色 */
                border-radius: 5px;
                min-height: 30px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: {DEFAULT_THEME_SECONDARY_TEXT};
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px; border: none; background: none;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                 background: none;
            }}
            /* 水平滚动条类似 */
            QScrollBar:horizontal {{
                background: {DEFAULT_THEME_BACKGROUND}; height: 10px; border-radius: 5px; margin: 0px;
            }}
            QScrollBar::handle:horizontal {{
                background: {DEFAULT_THEME_BORDER}; border-radius: 5px; min-width: 30px;
            }}
            QScrollBar::handle:horizontal:hover {{ background: {DEFAULT_THEME_SECONDARY_TEXT}; }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{ width: 0px; border: none; background: none; }}
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{ background: none; }}

            QSplitter::handle {{
                background-color: {DEFAULT_THEME_BORDER};
            }}
            QSplitter::handle:horizontal {{
                width: 2px; /* 分割线宽度 */
            }}
            QSplitter::handle:hover {{
                background-color: {DEFAULT_THEME_ACCENT};
            }}
            /* 区块框架 */
            QFrame#SectionFrame, QFrame#DetailedAudioPlayerWidget, QFrame#OutputTrackEntry {{
                background-color: {DEFAULT_THEME_CARD_BG};
                border-radius: 12px; /* 统一圆角 */
                border: 1px solid {DEFAULT_THEME_BORDER};
                /* 可以添加细微的阴影，但QSS的阴影效果有限 */
            }}
            /* 文件选择区域 */
            QFrame#SelectAreaFrame {{
                background-color: {DEFAULT_THEME_SECONDARY_BG};
                border: 2px dashed {DEFAULT_THEME_ACCENT};
                border-radius: 12px;
            }}
            QFrame#SelectAreaFrame:hover {{
                border-color: {DEFAULT_THEME_ACCENT_HOVER};
                background-color: {DEFAULT_THEME_CARD_BG}; /* 悬停时背景变深一点 */
            }}
            /* 已选文件信息 */
            QFrame#SelectedFileFrame {{
                background-color: {DEFAULT_THEME_CARD_BG};
                border-radius: 10px;
                border: 1px solid {DEFAULT_THEME_BORDER};
            }}
            /* 波形图画布 */
            QFrame#WaveformCanvas {{
                background-color: {DEFAULT_THEME_WAVEFORM_BG};
                border-radius: 10px;
                border: 1px solid {DEFAULT_THEME_BORDER};
            }}
            /* 滚动区域内部的QWidget */
            QWidget#ScrollAreaWidget {{
                background-color: {DEFAULT_THEME_BACKGROUND}; /* 确保滚动区域背景一致 */
            }}
            /* 标签分组标题 */
            QLabel#GroupTitleLabel {{
                 font-size: {LABEL_FONT_SIZE}pt;
                 font-weight: bold;
                 color: {DEFAULT_THEME_SECONDARY_TEXT};
                 padding-top: 5px;
                 padding-bottom: 3px;
            }}
            /* 对话框样式 */
            QMessageBox {{
                background-color: {DEFAULT_THEME_CARD_BG};
            }}
            QMessageBox QLabel {{
                color: #FFFFFF; /* 纯黑色文字 */
                font-size: {LABEL_FONT_SIZE + 1}pt;
            }}
            QMessageBox QPushButton {{
                min-width: 80px;
                min-height: 30px;
            }}
        """)

    def _create_styled_label(self, text, font_size, bold=False, object_name=None):
        label = QLabel(text)
        font = QFont(FONT_FAMILY, font_size, QFont.Bold if bold else QFont.Normal)
        label.setFont(font)
        if object_name:
            label.setObjectName(object_name)
        return label

    def _create_left_panel_content(self, parent_container):
        left_panel_layout = QVBoxLayout(parent_container)
        left_panel_layout.setContentsMargins(15, 15, 15, 15) # 统一边距
        left_panel_layout.setSpacing(20) # 控件间距

        # 添加LOGO到左侧面板顶部
        logo_label = QLabel()
        logo_pixmap = QPixmap(os.path.join("assets", "images", "logo.png"))
        # 获取左侧面板的宽度
        panel_width = parent_container.width()
        # 计算目标宽度（面板宽度的1/3）
        target_width = panel_width // 3
        # 等比例缩放图片
        scaled_pixmap = logo_pixmap.scaled(target_width, target_width, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        logo_label.setPixmap(scaled_pixmap)
        logo_label.setAlignment(Qt.AlignCenter)
        # 设置大小策略以允许自动调整
        logo_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        # 添加重绘事件以处理窗口大小变化
        logo_label.resizeEvent = lambda _: logo_label.setPixmap(logo_pixmap.scaled(parent_container.width() // 3, parent_container.width() // 3, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        left_panel_layout.addWidget(logo_label)

        # 创建成品歌曲部分
        finished_song_frame = QFrame()
        finished_song_frame.setObjectName("SectionFrame")
        finished_song_layout = QVBoxLayout(finished_song_frame)
        finished_song_layout.setContentsMargins(15,15,15,15)
        finished_song_layout.setSpacing(15) # 区块内间距

        # 成品歌曲标题
        label_finished = self._create_styled_label("歌曲管理", SECTION_HEADER_FONT_SIZE, True)
        finished_song_layout.addWidget(label_finished)

        # 成品歌曲滚动区域
        self.songs_scroll_area = QScrollArea()
        self.songs_scroll_area.setWidgetResizable(True)
        self.songs_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.songs_scroll_area.setStyleSheet("background: transparent; border: none;")

        self.songs_container = QWidget()
        self.songs_container.setObjectName("SongsContainer")
        self.songs_container_layout = QVBoxLayout(self.songs_container)
        self.songs_container_layout.setContentsMargins(0,0,0,0)
        self.songs_container_layout.setSpacing(8)  # 歌曲条目间距
        self.songs_container_layout.setAlignment(Qt.AlignTop)  # 顶部对齐

        # 初始提示信息
        self.no_songs_label = QLabel("暂无成品歌曲")
        self.no_songs_label.setStyleSheet(f"color: {DEFAULT_THEME_SECONDARY_TEXT}; padding: 20px 0px;")
        self.no_songs_label.setAlignment(Qt.AlignCenter)
        self.songs_container_layout.addWidget(self.no_songs_label)

        self.songs_scroll_area.setWidget(self.songs_container)
        self.songs_scroll_area.setMinimumHeight(200)  # 设置最小高度
        self.songs_scroll_area.setMaximumHeight(300)  # 设置最大高度

        finished_song_layout.addWidget(self.songs_scroll_area)

        left_panel_layout.addWidget(finished_song_frame)

        # 创建源音频文件部分
        input_section_frame = QFrame()
        input_section_frame.setObjectName("SectionFrame")
        input_section_layout = QVBoxLayout(input_section_frame)
        input_section_layout.setContentsMargins(15,15,15,15)
        input_section_layout.setSpacing(15) # 区块内间距

        label1 = self._create_styled_label("上传音频", SECTION_HEADER_FONT_SIZE, True)
        input_section_layout.addWidget(label1)
        self.input_file_selector = InputFileSelectionWidget(input_section_frame, app_instance=self)
        input_section_layout.addWidget(self.input_file_selector)

        # 处理模式选择
        mode_selection_layout = QHBoxLayout()
        mode_label = self._create_styled_label("处理模式:", CONTROL_FONT_SIZE +1)
        mode_selection_layout.addWidget(mode_label)
        self.processing_mode_combo = QComboBox()
        self.processing_mode_combo.addItems(["完整模式", "干声模式"])
        mode_selection_layout.addWidget(self.processing_mode_combo, 1)
        input_section_layout.addLayout(mode_selection_layout)

        # 输出格式选择
        format_selection_layout = QHBoxLayout()
        format_label = self._create_styled_label("输出格式:", CONTROL_FONT_SIZE +1)
        format_selection_layout.addWidget(format_label)
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["WAV", "MP3"])
        self.output_format_combo.setToolTip("选择音频输出格式")
        format_selection_layout.addWidget(self.output_format_combo, 1)
        input_section_layout.addLayout(format_selection_layout)

        left_panel_layout.addWidget(input_section_frame)

        # 添加一键翻唱按钮
        self._create_main_action_button(left_panel_layout)

    def load_finished_songs(self):
        """加载outputs目录下的成品歌曲"""
        self.logger.info("加载成品歌曲列表")

        # 获取outputs目录
        outputs_dir = os.path.join(os.getcwd(), "outputs")
        if not os.path.exists(outputs_dir):
            self.logger.info("outputs目录不存在，创建目录")
            os.makedirs(outputs_dir)
            return

        # 清空当前列表
        self.finished_songs = []

        # 遍历outputs目录下的所有子目录
        song_folders = []
        for item in os.listdir(outputs_dir):
            item_path = os.path.join(outputs_dir, item)
            if os.path.isdir(item_path):
                # 获取目录创建时间
                creation_time = os.path.getctime(item_path)
                song_folders.append((item, item_path, creation_time))

        # 按创建时间降序排序（最新的排在前面）
        song_folders.sort(key=lambda x: x[2], reverse=True)

        # 处理每个歌曲目录
        for song_name, song_path, creation_time in song_folders:
            # 查找混音文件，作为歌曲的主文件
            mix_file = None
            model_used = None

            for file in os.listdir(song_path):
                if "混缩" in file or "混音" in file:
                    mix_file = os.path.join(song_path, file)

                # 查找包含模型名称的文件，通常转换人声文件名会包含模型信息
                if "转换人声" in file:
                    # 从文件名中尝试提取模型名称
                    try:
                        model_file = os.path.basename(file)
                        # 先尝试从convert_voice_dry中保存的文件名中获取模型名称
                        # 如果使用了模型，文件名格式应该是：原始文件名+可能的音高信息+"-转换人声.wav"

                        # 获取最近使用的模型名称
                        model_files = self._get_files_from_models_dir(extension=".pt")
                        # 检查model_combo中当前选择的模型
                        if hasattr(self, 'model_combo') and self.model_combo.currentText() != "无模型文件":
                            current_model = self.model_combo.currentText()
                            model_name = os.path.splitext(current_model)[0]
                            model_used = model_name
                        # 如果未找到，尝试检查历史保存的处理记录
                        elif os.path.exists(os.path.join(song_path, "process_info.json")):
                            try:
                                with open(os.path.join(song_path, "process_info.json"), "r") as f:
                                    process_info = json.load(f)
                                    if "model" in process_info:
                                        model_used = process_info["model"]
                            except:
                                pass
                        # 如果仍未找到，尝试从文件名中提取
                        if not model_used:
                            for model in model_files:
                                model_name = os.path.splitext(model)[0]
                                if model_name in model_file:
                                    model_used = model_name
                                    break
                    except Exception as e:
                        self.logger.warning(f"提取模型名称出错: {str(e)}")

            # 如果没有找到混音文件，查找转换人声文件
            if not mix_file:
                for file in os.listdir(song_path):
                    if "转换人声" in file:
                        mix_file = os.path.join(song_path, file)
                        break

            # 如果仍没找到，使用第一个音频文件
            if not mix_file:
                for file in os.listdir(song_path):
                    file_path = os.path.join(song_path, file)
                    if os.path.isfile(file_path) and file.lower().endswith(('.wav', '.mp3', '.ogg')):
                        mix_file = file_path
                        break

            # 如果找到了歌曲文件，添加到列表
            if mix_file:
                # 格式化创建时间
                created_time_str = time.strftime("%Y/%m/%d %H:%M", time.localtime(creation_time))

                # 创建歌曲数据
                song_data = {
                    'name': song_name,
                    'dir_path': song_path,
                    'main_file': mix_file,
                    'created_time': created_time_str,
                    'model': model_used if model_used else "未知模型"
                }

                self.finished_songs.append(song_data)

        # 更新UI显示
        self._update_finished_songs_ui()

    def _update_finished_songs_ui(self):
        """更新成品歌曲UI显示"""
        # 清空现有的歌曲条目
        for i in reversed(range(self.songs_container_layout.count())):
            item = self.songs_container_layout.itemAt(i)
            widget = item.widget()
            if widget:
                widget.deleteLater()

        # 如果没有歌曲，显示提示信息
        if not self.finished_songs:
            self.no_songs_label = QLabel("暂无成品歌曲")
            self.no_songs_label.setStyleSheet(f"color: {DEFAULT_THEME_SECONDARY_TEXT}; padding: 20px 0px;")
            self.no_songs_label.setAlignment(Qt.AlignCenter)
            self.songs_container_layout.addWidget(self.no_songs_label)
            return

        # 添加每首歌曲的条目
        for song_data in self.finished_songs:
            song_entry = FinishedSongEntryWidget(
                self.songs_container,
                song_data,
                self._play_finished_song,
                self._show_song_context_menu
            )
            self.songs_container_layout.addWidget(song_entry)

        # 添加弹性空间，使条目顶部对齐
        self.songs_container_layout.addStretch(1)

    def _play_finished_song(self, song_data):
        """播放选中的成品歌曲"""
        self.logger.info(f"播放成品歌曲: {song_data['name']}")

        # 清空现有的显示内容
        self._clear_output_track_list()

        # 获取歌曲目录中的所有音频文件
        dir_path = song_data['dir_path']
        audio_files = []

        # 查找混音文件
        mix_files = []
        vocal_files = []
        instrumental_files = []
        harmony_files = []
        other_files = []

        for file in os.listdir(dir_path):
            file_path = os.path.join(dir_path, file)
            if os.path.isfile(file_path) and file.lower().endswith(('.wav', '.mp3', '.ogg')):
                if "混缩" in file or "混音" in file:
                    mix_files.append((file, file_path))
                elif "转换人声" in file:
                    vocal_files.append((file, file_path))
                elif "伴奏" in file:
                    instrumental_files.append((file, file_path))
                elif "和声" in file:
                    harmony_files.append((file, file_path))
                else:
                    other_files.append((file, file_path))

        # 按文件名优先级组合所有文件
        audio_files = mix_files + vocal_files + instrumental_files + harmony_files + other_files

        if not audio_files:
            QMessageBox.warning(self, "无音频文件", f"在 {song_data['name']} 目录中未找到音频文件。")
            return

        # 构建音轨数据
        track_data_list = []

        # 添加序号前缀
        for idx, (file_name, file_path) in enumerate(audio_files, 1):
            # 创建简洁的显示名称，去掉文件扩展名
            display_name = os.path.splitext(file_name)[0]

            # 根据文件类型添加图标或前缀
            prefix = ""
            if "混缩" in file_name or "混音" in file_name:
                prefix = "① "
            elif "转换人声" in file_name:
                prefix = "② "
            elif "伴奏" in file_name:
                prefix = "③ "
            elif "和声" in file_name:
                prefix = "④ "
            else:
                prefix = f"{idx} "

            track_data = {
                'id': f'song_{idx}',
                'title': f"{prefix}{display_name}",
                'filepath': file_path,
                'metadata_text': f'来源: {song_data["name"]}, 创建时间: {song_data["created_time"]}'
            }
            track_data_list.append(track_data)

        # 更新UI显示
        for track_data in track_data_list:
            entry = OutputTrackEntryWidget(
                self.output_track_list_frame,
                track_data,
                self.handle_output_track_play,
                self.handle_output_track_download
            )
            self.output_track_list_layout.addWidget(entry)

        # 播放第一个文件
        if track_data_list:
            self.handle_output_track_play(track_data_list[0])

    def _show_song_context_menu(self, song_data, position):
        """显示成品歌曲的上下文菜单"""
        self.logger.info(f"显示歌曲上下文菜单: {song_data['name']}")

        # 创建菜单
        context_menu = QMenu(self)
        context_menu.setStyleSheet(f"""
            QMenu {{
                background-color: {DEFAULT_THEME_CARD_BG};
                color: {DEFAULT_THEME_FOREGROUND};
                border: 1px solid {DEFAULT_THEME_BORDER};
                border-radius: 6px;
                padding: 5px;
            }}
            QMenu::item {{
                padding: 8px 20px;
                border-radius: 4px;
            }}
            QMenu::item:selected {{
                background-color: {DEFAULT_THEME_ACCENT};
            }}
        """)

        # 添加菜单项
        open_folder_action = QAction("打开文件夹", self)
        open_folder_action.triggered.connect(lambda: self._open_song_folder(song_data))

        delete_action = QAction("❌ 删除", self)  # 使用文本前缀区分删除操作
        delete_action.triggered.connect(lambda: self._delete_song(song_data))

        # 添加动作到菜单
        context_menu.addAction(open_folder_action)
        context_menu.addSeparator()
        context_menu.addAction(delete_action)

        # 显示菜单
        context_menu.exec_(position)

    def _open_song_folder(self, song_data):
        """在资源管理器中打开歌曲文件夹"""
        self.logger.info(f"打开歌曲文件夹: {song_data['dir_path']}")

        try:
            # 使用系统默认文件浏览器打开目录
            # 目前仅支持Windows平台，其他平台支持将在未来添加
            os.startfile(song_data['dir_path'])
        except Exception as e:
            self.logger.error(f"打开文件夹失败: {str(e)}")
            QMessageBox.critical(self, "打开失败", f"无法打开文件夹: {str(e)}")

    def _delete_song(self, song_data):
        """删除歌曲"""
        self.logger.info(f"准备删除歌曲: {song_data['name']}")

        # 显示确认对话框
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除歌曲 '{song_data['name']}' 吗？\n此操作将永久删除歌曲文件夹及其内容。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 获取歌曲目录路径
                dir_path = song_data['dir_path']

                # 停止正在播放的媒体
                if hasattr(self, 'output_master_player') and self.output_master_player:
                    self.output_master_player.media_player.stop()
                    self.output_master_player.media_player.setMedia(QMediaContent())

                # 强制垃圾回收
                import gc
                gc.collect()

                # 短暂暂停，确保文件句柄释放
                time.sleep(0.2)

                # 删除目录及其内容
                shutil.rmtree(dir_path)
                self.logger.info(f"已删除歌曲目录: {dir_path}")

                # 更新成品歌曲列表
                self.load_finished_songs()

                # 清空右侧预览区（如果当前预览的是被删除的歌曲）
                self._clear_output_track_list()
                self.initial_output_message_label.setText("选择一首歌曲进行播放")
                self.initial_output_message_label.show()

                # 显示成功消息
                self.status_label.setText(f"已删除歌曲: {song_data['name']}")
                self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_SUCCESS};")

            except Exception as e:
                self.logger.error(f"删除歌曲失败: {str(e)}")
                QMessageBox.critical(self, "删除失败", f"无法删除歌曲: {str(e)}")

                # 显示错误消息
                self.status_label.setText(f"删除歌曲失败: {str(e)}")
                self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_ERROR};")

    def _create_right_panel_content(self, parent_container):
        right_panel_layout = QVBoxLayout(parent_container)
        right_panel_layout.setContentsMargins(10, 15, 15, 15) # 调整边距
        right_panel_layout.setSpacing(15) # 调整间距

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.main_output_widget = QWidget()
        self.main_output_widget.setObjectName("ScrollAreaWidget")
        main_output_layout = QVBoxLayout(self.main_output_widget)
        main_output_layout.setContentsMargins(10,10,10,10)
        main_output_layout.setSpacing(18) # 滚动区内间距

        self.output_master_player = DetailedAudioPlayerWidget(self.main_output_widget, show_title=True, show_metadata=True, remix_callback=self.open_remix_dialog)
        main_output_layout.addWidget(self.output_master_player)
        self.output_master_player.play_button.setEnabled(False)
        self.output_master_player.hide()  # 初始化时隐藏

        divider1 = QFrame()
        divider1.setFrameShape(QFrame.HLine); divider1.setFrameShadow(QFrame.Sunken)
        divider1.setFixedHeight(1); divider1.setStyleSheet(f"background-color: {DEFAULT_THEME_BORDER};")
        main_output_layout.addWidget(divider1)

        label_tracks = self._create_styled_label("翻唱分轨", TITLE_FONT_SIZE + 1, True)
        main_output_layout.addWidget(label_tracks)
        self.output_track_list_frame = QFrame()
        self.output_track_list_layout = QVBoxLayout(self.output_track_list_frame)
        self.output_track_list_layout.setContentsMargins(0,0,0,0)
        self.output_track_list_layout.setSpacing(10) # 音轨条目间距
        main_output_layout.addWidget(self.output_track_list_frame)
        self.output_track_list_frame.hide()  # 初始化时隐藏
        self.initial_output_message_label = self._create_styled_label("处理完成后，结果将显示在此处。", LABEL_FONT_SIZE)
        self.initial_output_message_label.setAlignment(Qt.AlignCenter)
        self.initial_output_message_label.setStyleSheet(f"color: {DEFAULT_THEME_SECONDARY_TEXT}; padding: 20px;")
        self.output_track_list_layout.addWidget(self.initial_output_message_label)

        # 在翻唱分轨下方添加模型音高和混响部分
        model_pitch_frame = QFrame()
        model_pitch_layout = QVBoxLayout(model_pitch_frame)
        model_pitch_layout.setContentsMargins(0,0,0,0)
        model_pitch_layout.setSpacing(0)
        self._create_model_and_pitch_section(model_pitch_layout)
        main_output_layout.addWidget(model_pitch_frame)

        self._create_config_options_section(main_output_layout)
        main_output_layout.addStretch(1)
        scroll_area.setWidget(self.main_output_widget)
        right_panel_layout.addWidget(scroll_area)

    def open_remix_dialog(self, _):
        """打开重新混音对话框"""
        self.logger.info("打开重新混音对话框")

        # 获取当前的混响和音量参数
        room_size = self.room_size_slider.value() / 100.0
        damping = self.damping_slider.value() / 100.0
        wet_level = self.wet_level_slider.value() / 100.0
        dry_level = self.dry_level_slider.value() / 100.0

        # 默认音量设置
        vocal_volume = 0
        instrumental_volume = 0

        # 创建并显示对话框
        dialog = ReverbMixSettingsDialog(
            self,
            room_size=room_size,
            damping=damping,
            wet_level=wet_level,
            dry_level=dry_level,
            vocal_volume=vocal_volume,
            instrumental_volume=instrumental_volume
        )

        # 设置和声加入伴奏选项的状态
        dialog.harmony_to_accomp_check.setChecked(self.harmony_to_accomp_check.isChecked())

        if dialog.exec_() == QDialog.Accepted:
            # 获取用户设置的参数
            params = dialog.get_values()
            self.logger.info(f"用户设置的混音参数: {params}")

            # 更新主界面的混响滑块值（以便下次打开对话框时保持一致）
            self.room_size_slider.setValue(int(params['room_size'] * 100))
            self.damping_slider.setValue(int(params['damping'] * 100))
            self.wet_level_slider.setValue(int(params['wet_level'] * 100))
            self.dry_level_slider.setValue(int(params['dry_level'] * 100))

            # 更新和声加入伴奏复选框
            self.harmony_to_accomp_check.setChecked(params['harmony_to_accomp'])

            # 执行重新混音
            self.remix_audio(params)

    def remix_audio(self, params):
        """使用新的参数重新混音"""
        self.logger.info("开始重新混音")
        self.logger.info(f"混音参数: {params}")  # 打印传入的所有参数
        QTimer.singleShot(0, lambda: self.status_label.setText("重新混音中..."))

        try:
            # 获取需要混合的文件
            vocal_file = self.processed_files.get('converted_vocal')
            instrumental_file = self.processed_files.get('instrumental')
            harmony_file = self.processed_files.get('harmony')

            # 使用已经调整音高的文件（如果存在）
            if 'instrumental_shifted' in self.processed_files and self.instrumental_pitch_slider.value() != 0:
                instrumental_file = self.processed_files['instrumental_shifted']
                self.logger.info(f"使用已调整音高的伴奏文件: {instrumental_file}")

            harmony_to_accomp = params.get('harmony_to_accomp', self.harmony_to_accomp_check.isChecked())

            if 'harmony_shifted' in self.processed_files and self.instrumental_pitch_slider.value() != 0 and harmony_to_accomp:
                harmony_file = self.processed_files['harmony_shifted']
                self.logger.info(f"使用已调整音高的和声文件: {harmony_file}")

            if not vocal_file:
                error_msg = "未找到转换后的人声文件"
                self.logger.error(error_msg)
                QMessageBox.warning(self, "重新混音失败", error_msg)
                return

            if not instrumental_file:
                error_msg = "未找到伴奏文件"
                self.logger.error(error_msg)
                QMessageBox.warning(self, "重新混音失败", error_msg)
                return

            # 确保vocal_file是带混响的版本（如果启用了混响）
            if self.reverb_enabled_check.isChecked():
                # 首先应用新的混响参数生成新的混响人声文件
                converted_vocal_file = self.processed_files.get('converted_vocal')

                # 使用临时文件
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    reverb_vocal_file = temp_file.name

                # 读取音频
                audio = AudioSegment.from_file(converted_vocal_file)

                # 应用混响效果
                reverb_audio = add_reverb(
                    audio,
                    room_size=params['room_size'],
                    damping=params['damping'],
                    wet_level=params['wet_level'],
                    dry_level=params['dry_level']
                )

                # 保存混响后的音频
                reverb_audio.export(reverb_vocal_file, format="wav")

                self.logger.info(f"重新混响完成: {reverb_vocal_file}")
                vocal_file = reverb_vocal_file

            # 获取用户选择的输出格式
            output_format = self.output_format_combo.currentText().lower()

            # 使用带时间戳的临时文件名，避免文件占用冲突
            timestamp = int(time.time())

            # 原始混音文件，不管音高调整如何都使用原始文件名，确保重新混音始终覆盖相同的文件
            temp_mix_file = os.path.join(self.output_dir, f"{self.original_filename}-混缩-temp-{timestamp}.{output_format}")
            final_mix_file = os.path.join(self.output_dir, f"{self.original_filename}-混缩.{output_format}")

            # 确保文件路径正确
            if instrumental_file.startswith('temp/'):
                instrumental_file = os.path.join('uvr5', instrumental_file)
                self.logger.info(f"修正伴奏文件路径为: {instrumental_file}")

            if harmony_file and harmony_file.startswith('temp/'):
                harmony_file = os.path.join('uvr5', harmony_file)
                self.logger.info(f"修正和声文件路径为: {harmony_file}")

            # 读取音频
            vocal_audio = AudioSegment.from_file(vocal_file)
            instrumental_audio = AudioSegment.from_file(instrumental_file)

            # 使用新的音量参数调整音量
            vocal_volume = params['vocal_volume']
            instrumental_volume = params['instrumental_volume']

            # 详细记录音量调整过程
            self.logger.info(f"应用音量调整 - 人声: {vocal_volume} dB, 伴奏: {instrumental_volume} dB")
            self.logger.info(f"人声音频调整前响度: {vocal_audio.dBFS:.2f} dBFS")
            self.logger.info(f"伴奏音频调整前响度: {instrumental_audio.dBFS:.2f} dBFS")

            # 使用音量参数进行调整
            vocal_audio = vocal_audio + vocal_volume
            instrumental_audio = instrumental_audio + instrumental_volume

            # 记录调整后的音频信息
            self.logger.info(f"人声音频调整后响度: {vocal_audio.dBFS:.2f} dBFS")
            self.logger.info(f"伴奏音频调整后响度: {instrumental_audio.dBFS:.2f} dBFS")

            # 混合人声和伴奏
            self.logger.info("混合人声和伴奏...")
            mixed_audio = vocal_audio.overlay(instrumental_audio)

            # 如果启用了和声并且存在和声文件，则加入和声
            if harmony_to_accomp and harmony_file:
                self.logger.info("加入和声到混音")
                harmony_audio = AudioSegment.from_file(harmony_file)
                harmony_audio = harmony_audio + instrumental_volume # 和声音量与伴奏音量一致
                self.logger.info(f"和声音频调整后响度: {harmony_audio.dBFS:.2f} dBFS")
                mixed_audio = mixed_audio.overlay(harmony_audio)

            # 记录最终混音信息
            self.logger.info(f"最终混音音频响度: {mixed_audio.dBFS:.2f} dBFS")

            # 在保存最终文件前，确保停止播放和释放文件句柄
            if hasattr(self, 'output_master_player') and self.output_master_player:
                self.output_master_player.media_player.stop()
                self.output_master_player.media_player.setMedia(QMediaContent())

            # 强制垃圾回收
            import gc
            gc.collect()

            # 短暂等待，确保文件句柄已释放
            time.sleep(0.1)

            # 先保存到临时文件
            if output_format == "wav":
                mixed_audio.export(temp_mix_file, format="wav")
            elif output_format == "mp3":
                mixed_audio.export(temp_mix_file, format="mp3", bitrate="320k")
            else:
                # 默认使用WAV格式
                temp_mix_file = os.path.join(self.output_dir, f"{self.original_filename}-混缩-temp-{timestamp}.wav")
                mixed_audio.export(temp_mix_file, format="wav")

            self.logger.info(f"临时混音文件保存完成: {temp_mix_file}")

            # 删除原始混音文件（如果存在且不被占用）
            try:
                if os.path.exists(final_mix_file):
                    os.unlink(final_mix_file)
                    self.logger.info(f"已删除原混音文件: {final_mix_file}")
            except Exception as e:
                self.logger.warning(f"无法删除原混音文件，将使用新文件名: {str(e)}")
                # 如果删除失败，使用带时间戳的文件名作为最终名称
                final_mix_file = temp_mix_file

            # 如果成功删除原文件，则重命名临时文件
            if final_mix_file != temp_mix_file:
                try:
                    os.rename(temp_mix_file, final_mix_file)
                    self.logger.info(f"已将临时文件重命名为: {final_mix_file}")
                except Exception as e:
                    self.logger.warning(f"重命名文件失败，将使用临时文件名: {str(e)}")
                    final_mix_file = temp_mix_file

            self.logger.info(f"重新混音完成: {final_mix_file}")
            self.processed_files['final_mix'] = final_mix_file

            # 删除临时文件
            if 'reverb_vocal_file' in locals() and os.path.exists(reverb_vocal_file):
                try:
                    os.unlink(reverb_vocal_file)
                    self.logger.info(f"已删除临时混响文件: {reverb_vocal_file}")
                except Exception as e:
                    self.logger.warning(f"删除临时混响文件失败: {str(e)}")

            # 更新界面显示
            self.status_label.setText("重新混音完成")
            self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_SUCCESS};")

            # 创建确保唯一的文件名，不覆盖原文件
            # 使用微秒级时间戳确保唯一性，避免多次混音时文件名冲突
            unique_timestamp = int(time.time() * 1000000)
            unique_mix_file = os.path.join(self.output_dir, f"{self.original_filename}-混缩-{unique_timestamp}.{output_format}")

            # 复制已创建的混音文件到唯一命名的文件
            try:
                shutil.copy2(final_mix_file, unique_mix_file)
                self.logger.info(f"已创建唯一混音文件: {unique_mix_file}")
                # 更新处理文件字典，保存唯一的新文件路径
                self.processed_files['final_mix'] = unique_mix_file
            except Exception as e:
                self.logger.warning(f"创建唯一混音文件失败，使用原文件: {str(e)}")
                # 如果复制失败，继续使用原文件

            # 更新输出界面
            self._display_processed_results()

            # 手动更新播放器并播放最新的混音文件 - 使用更长延迟确保文件完全写入
            if 'final_mix' in self.processed_files:
                # 创建轨道数据
                track_data = {
                    'id': 'mix',
                    'title': '① 最终混音',
                    'filepath': self.processed_files['final_mix'],
                    'metadata': f'处理模式: {self.processing_mode_combo.currentText()}, 文件: {os.path.basename(self.processed_files["final_mix"])}'
                }

                # 使用较长延迟确保文件系统操作完成，然后播放最新文件
                QTimer.singleShot(500, lambda: self.handle_output_track_play(track_data))

        except Exception as e:
            self.logger.error(f"重新混音过程中出错: {str(e)}")
            self.status_label.setText(f"重新混音失败: {str(e)}")
            self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_ERROR};")
            QMessageBox.warning(self, "重新混音失败", str(e))

    def open_file_dialog(self, callback_on_selection):
        filepath, _ = QFileDialog.getOpenFileName(
            self, "选择音频文件", "", "Audio Files (*.wav *.mp3 *.ogg);;All files (*.*)"
        )
        self.selected_audio_filepath = filepath if filepath else None
        if callback_on_selection:
            callback_on_selection(self.selected_audio_filepath)

    def check_activation(self):
        """检查软件激活状态，如果未激活则显示激活对话框"""
        self.logger.info("检查软件激活状态")

        # 检查是否已激活
        if activation_utils.is_activated():
            self.logger.info("软件已激活")
            return True

        self.logger.info("软件未激活，显示激活对话框")

        # 显示激活对话框
        activation_dialog = ActivationDialog(self)
        result = activation_dialog.exec_()

        if result == QDialog.Accepted:
            self.logger.info("激活成功")
            return True
        else:
            self.logger.warning("激活失败或被取消，退出应用")
            # 退出应用
            sys.exit(0)

    def _create_slider_with_label(self, parent_layout, label_text, min_val, max_val, initial_val, steps=0, is_int=True):
        main_text_label = self._create_styled_label(label_text, CONTROL_FONT_SIZE)
        parent_layout.addWidget(main_text_label)

        slider_frame = QFrame()
        slider_frame_layout = QHBoxLayout(slider_frame)
        slider_frame_layout.setContentsMargins(0,5,0,5) # 给滑块上下一点空间
        slider_frame_layout.setSpacing(10)

        slider = QSlider(Qt.Horizontal)
        slider.setRange(min_val, max_val)
        if is_int:
            slider.setValue(initial_val)
        else:
            slider.setValue(int(initial_val * 100))

        if steps > 0 and is_int:
             slider.setTickInterval((max_val - min_val) // steps if steps !=0 else 1)
             slider.setSingleStep(1)

        value_display_label = self._create_styled_label(str(initial_val), CONTROL_FONT_SIZE)
        value_display_label.setMinimumWidth(35) # 调整宽度
        value_display_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        value_display_label.setStyleSheet(f"color: {DEFAULT_THEME_SECONDARY_TEXT};")


        if is_int:
            slider.valueChanged.connect(lambda val, lbl=value_display_label: lbl.setText(str(val)))
        else:
            value_display_label.setText(f"{initial_val:.2f}")
            slider.valueChanged.connect(lambda val, lbl=value_display_label: lbl.setText(f"{val/100.0:.2f}"))

        slider_frame_layout.addWidget(slider, 1)
        slider_frame_layout.addWidget(value_display_label)
        parent_layout.addWidget(slider_frame)
        return slider

    def _create_model_and_pitch_section(self, parent_layout_or_widget):
        section_frame = QFrame() # 将模型和音高设置放入一个卡片中
        section_frame.setObjectName("SectionFrame")
        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15,15,15,15)
        section_layout.setSpacing(15)

        section_label = self._create_styled_label("模型与音高", SECTION_HEADER_FONT_SIZE, True)
        section_layout.addWidget(section_label)

        # 音色模型和配置文件并排显示
        model_config_row = QFrame()
        model_config_layout = QHBoxLayout(model_config_row)
        model_config_layout.setContentsMargins(0,0,0,0)
        model_config_layout.setSpacing(15)

        # 左侧音色模型
        model_widget = QWidget()
        model_layout = QVBoxLayout(model_widget)
        model_layout.setContentsMargins(0,0,0,0)
        model_layout.setSpacing(5)
        model_label = self._create_styled_label("音色模型 (.pt):", CONTROL_FONT_SIZE + 1)
        model_layout.addWidget(model_label)
        self.model_files = self._get_files_from_models_dir(extension=".pt")
        if not self.model_files: self.model_files = ["无模型文件"]
        self.model_combo = QComboBox()
        self.model_combo.addItems(self.model_files)
        self.model_combo.currentTextChanged.connect(self._on_model_selected)
        model_layout.addWidget(self.model_combo)
        model_config_layout.addWidget(model_widget, 1)

        # 右侧配置文件
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setContentsMargins(0,0,0,0)
        config_layout.setSpacing(5)
        config_label = self._create_styled_label("配置文件 (.yaml):", CONTROL_FONT_SIZE + 1)
        config_layout.addWidget(config_label)
        self.config_files = self._get_files_from_models_dir(extension=".yaml")
        if not self.config_files: self.config_files = ["无配置文件"]
        self.config_combo = QComboBox()
        self.config_combo.addItems(self.config_files)
        config_layout.addWidget(self.config_combo)
        model_config_layout.addWidget(config_widget, 1)

        section_layout.addWidget(model_config_row)
        section_layout.addSpacing(10)

        # 人声音高和伴奏音高并排显示
        pitch_row = QFrame()
        pitch_layout = QHBoxLayout(pitch_row)
        pitch_layout.setContentsMargins(0,0,0,0)
        pitch_layout.setSpacing(15)

        # 左侧人声音高
        vocal_pitch_widget = QWidget()
        vocal_pitch_layout = QVBoxLayout(vocal_pitch_widget)
        vocal_pitch_layout.setContentsMargins(0,0,0,0)
        vocal_pitch_layout.setSpacing(5)
        self.vocal_pitch_slider = self._create_slider_with_label(vocal_pitch_layout, "人声音高:", -18, 18, 0, 36)
        pitch_layout.addWidget(vocal_pitch_widget, 1)

        # 右侧伴奏音高
        instrumental_pitch_widget = QWidget()
        instrumental_pitch_layout = QVBoxLayout(instrumental_pitch_widget)
        instrumental_pitch_layout.setContentsMargins(0,0,0,0)
        instrumental_pitch_layout.setSpacing(5)
        self.instrumental_pitch_slider = self._create_slider_with_label(instrumental_pitch_layout, "伴奏音高:", -18, 18, 0, 36)
        pitch_layout.addWidget(instrumental_pitch_widget, 1)

        section_layout.addWidget(pitch_row)

        # --- 混响与和声 ---
        reverb_harmony_label = self._create_styled_label("混响与和声", TITLE_FONT_SIZE + 1, True)
        section_layout.addWidget(reverb_harmony_label)

        checkbox_row = QFrame()
        checkbox_layout = QHBoxLayout(checkbox_row)
        checkbox_layout.setContentsMargins(0,5,0,5)
        checkbox_layout.setSpacing(20)
        self.reverb_enabled_check = QCheckBox("启用混响"); self.reverb_enabled_check.setChecked(False)  # 默认关闭混响
        self.reverb_enabled_check.toggled.connect(self._toggle_reverb_controls)
        checkbox_layout.addWidget(self.reverb_enabled_check)
        self.harmony_to_accomp_check = QCheckBox("和声加入伴奏")
        checkbox_layout.addWidget(self.harmony_to_accomp_check); checkbox_layout.addStretch(1)
        section_layout.addWidget(checkbox_row)

        self.reverb_controls_frame = QFrame()
        reverb_controls_layout_grid = QHBoxLayout(self.reverb_controls_frame) # Use grid for reverb sliders
        reverb_controls_layout_grid.setContentsMargins(0,0,0,0)
        reverb_controls_layout_grid.setSpacing(15)

        reverb_col1_widget = QWidget()
        reverb_col1_layout = QVBoxLayout(reverb_col1_widget)
        reverb_col1_layout.setContentsMargins(0,0,0,0); reverb_col1_layout.setSpacing(5)
        self.room_size_slider = self._create_slider_with_label(reverb_col1_layout, "房间大小:", 0, 100, 0.6, is_int=False)  # 房间大小为0.6
        self.wet_level_slider = self._create_slider_with_label(reverb_col1_layout, "湿润度:", 0, 100, 0.3, is_int=False)  # 湿润度为0.3
        reverb_col1_layout.addStretch(1)

        reverb_col2_widget = QWidget()
        reverb_col2_layout = QVBoxLayout(reverb_col2_widget)
        reverb_col2_layout.setContentsMargins(0,0,0,0); reverb_col2_layout.setSpacing(5)
        self.damping_slider = self._create_slider_with_label(reverb_col2_layout, "阻尼:", 0, 100, 0.1, is_int=False)  # 阻尼为0.1
        self.dry_level_slider = self._create_slider_with_label(reverb_col2_layout, "干燥度:", 0, 100, 0.9, is_int=False)  # 干燥度为0.9
        reverb_col2_layout.addStretch(1)

        reverb_controls_layout_grid.addWidget(reverb_col1_widget,1)
        reverb_controls_layout_grid.addWidget(reverb_col2_widget,1)

        section_layout.addWidget(self.reverb_controls_frame)
        self._toggle_reverb_controls(self.reverb_enabled_check.isChecked())

        self._on_model_selected(self.model_combo.currentText())

        if isinstance(parent_layout_or_widget, QVBoxLayout):
            parent_layout_or_widget.addWidget(section_frame)
        else: # Should not happen with current structure
            layout = QVBoxLayout(parent_layout_or_widget)
            layout.addWidget(section_frame)


    def _get_files_from_models_dir(self, extension=".pt"):
        models_dir = "models"
        if not os.path.exists(models_dir): return []
        files = sorted([f for f in os.listdir(models_dir) if f.endswith(extension) and os.path.isfile(os.path.join(models_dir, f))])
        return files

    def _on_model_selected(self, selected_model_name):
        if selected_model_name == "无模型文件":
            if self.config_files and "无配置文件" in self.config_files:
                 self.config_combo.setCurrentText("无配置文件")
            elif self.config_files:
                 self.config_combo.setCurrentIndex(0)
            return
        base_name = os.path.splitext(selected_model_name)[0]
        potential_config_name = base_name + ".yaml"
        if potential_config_name in self.config_files:
            self.config_combo.setCurrentText(potential_config_name)
        elif self.config_files and "无配置文件" not in self.config_files :
             self.config_combo.setCurrentIndex(0)
        elif "无配置文件" in self.config_files:
             self.config_combo.setCurrentText("无配置文件")

    def _create_main_action_button(self, parent_layout):
        action_frame = QFrame() # No card style for this, just a container
        action_layout = QVBoxLayout(action_frame)
        action_layout.setContentsMargins(0,10,0,0) # Add some top margin
        action_layout.setSpacing(10)

        self.process_button = QPushButton("🚀 一键翻唱")
        self.process_button.setObjectName("MainActionButton") # For specific styling
        self.process_button.setMinimumHeight(55) # Taller button
        self.process_button.clicked.connect(self.start_processing)
        action_layout.addWidget(self.process_button)

        self.status_label = self._create_styled_label("状态: 空闲", CONTROL_FONT_SIZE)
        self.status_label.setAlignment(Qt.AlignCenter) # 居中状态文本
        action_layout.addWidget(self.status_label)

        parent_layout.addWidget(action_frame)

    def _create_config_options_section(self, parent_layout):
        config_frame = QFrame()
        config_frame.setObjectName("SectionFrame")
        config_outer_layout = QVBoxLayout(config_frame)
        config_outer_layout.setContentsMargins(15,15,15,15)
        config_outer_layout.setSpacing(18) # 增加间距

        label_adv = self._create_styled_label("高级参数配置", SECTION_HEADER_FONT_SIZE, True)
        config_outer_layout.addWidget(label_adv)

        # --- Helper for creating labeled dropdowns ---
        def create_labeled_combo(layout, label_text, items):
            combo_label = self._create_styled_label(label_text, CONTROL_FONT_SIZE, object_name="GroupTitleLabel")
            layout.addWidget(combo_label)
            combo = QComboBox()
            combo.addItems(items)
            layout.addWidget(combo)
            return combo

        # --- Layout for parameters in two columns ---
        params_grid_layout = QHBoxLayout() # Use QHBoxLayout for columns
        params_grid_layout.setSpacing(20)

        left_col_widget = QWidget()
        left_col_layout = QVBoxLayout(left_col_widget)
        left_col_layout.setContentsMargins(0,0,0,0)
        left_col_layout.setSpacing(10)

        right_col_widget = QWidget()
        right_col_layout = QVBoxLayout(right_col_widget)
        right_col_layout.setContentsMargins(0,0,0,0)
        right_col_layout.setSpacing(10)

        # Column 1
        self.vocoder_combo = create_labeled_combo(left_col_layout, "声码器:", ["pc_nsf_hifigan_testing", "kouon_pc", "nsf_hifigan"])
        self.formant_shift_slider = self._create_slider_with_label(left_col_layout, "共振峰偏移:", -6, 6, 0, 12)
        self.method_combo = create_labeled_combo(left_col_layout, "采样器:", ["euler", "rk4"])
        left_col_layout.addStretch(1)


        # Column 2
        self.f0_extractor_combo = create_labeled_combo(right_col_layout, "F0提取器:", ["rmvpe (默认)", "parselmouth", "dio", "harvest", "crepe", "fcpe"])

        infer_step_label = self._create_styled_label("采样步数:", CONTROL_FONT_SIZE, object_name="GroupTitleLabel")
        right_col_layout.addWidget(infer_step_label)
        self.infer_step_entry = QLineEdit("50")
        # self.infer_step_entry.setFixedWidth(100) # Removed fixed width for better flow
        right_col_layout.addWidget(self.infer_step_entry)

        self.device_combo = create_labeled_combo(right_col_layout, "设备选择:", ["CUDA (默认)", "CPU"])
        right_col_layout.addStretch(1)

        params_grid_layout.addWidget(left_col_widget, 1)
        params_grid_layout.addWidget(right_col_widget, 1)
        config_outer_layout.addLayout(params_grid_layout)


        # 高级参数配置区域（已移除混响与和声部分）
        parent_layout.addWidget(config_frame)

    def _toggle_reverb_controls(self, enabled):
        self.reverb_controls_frame.setEnabled(enabled) # Disabling parent frame disables children

    def _clear_output_track_list(self):
        for i in reversed(range(self.output_track_list_layout.count())):
            item = self.output_track_list_layout.itemAt(i)
            widget = item.widget()
            if widget and widget != self.initial_output_message_label:
                self.output_track_list_layout.removeWidget(widget)
                widget.deleteLater()
        if self.initial_output_message_label:
            if not self.initial_output_message_label.parentWidget() == self.output_track_list_frame:
                 self.output_track_list_layout.addWidget(self.initial_output_message_label)
            self.initial_output_message_label.hide()
        self.output_master_player.hide()  # 清空时隐藏
        self.output_track_list_frame.hide()  # 清空时隐藏

    def _display_processed_results(self):
        self._clear_output_track_list()
        self.output_master_player.show()  # 显示主播放器
        self.output_track_list_frame.show()  # 显示分轨区

        # 根据处理模式构建输出轨道数据
        output_tracks_data = []
        mode = self.processing_mode_combo.currentText()

        # 仅在完整模式下添加最终混音文件
        if mode == "完整模式" and 'final_mix' in self.processed_files:
            final_mix_file = self.processed_files['final_mix']
            if os.path.exists(final_mix_file):
                output_tracks_data.append({
                    'id': 'mix',  # 设置id为'mix'，确保显示重新混音按钮
                    'title': '① 最终混音',
                    'filepath': final_mix_file,
                    'metadata': f'处理模式: {mode}, 文件: {os.path.basename(final_mix_file)}'
                })

        # 添加转换后的人声
        if 'converted_vocal' in self.processed_files:
            output_tracks_data.append({
                'id': 'vocals_conv',
                'title': '② 转换后的人声' if mode == "完整模式" else '① 转换后的人声',  # 干声模式下使用①
                'filepath': self.processed_files['converted_vocal'],
                'metadata': f'模型: {self.model_combo.currentText()}, 音高: {self.vocal_pitch_slider.value()}'
            })

        # 完整模式下，添加原始分离的人声、伴奏和和声
        if mode == "完整模式":
            # 原始分离的人声
            if 'original_vocal' in self.processed_files:
                output_tracks_data.append({
                    'id': 'vocals_original',
                    'title': '③ 分离的原始人声',
                    'filepath': self.processed_files['original_vocal'],
                    'metadata': f'文件: {os.path.basename(self.processed_files["original_vocal"])}'
                })

            # 伴奏 - 优先显示调整音高后的版本（如果存在）
            if 'instrumental_shifted' in self.processed_files:
                output_tracks_data.append({
                    'id': 'instrumental_shifted',
                    'title': '④ 伴奏(已调整音高)',
                    'filepath': self.processed_files['instrumental_shifted'],
                    'metadata': f'文件: {os.path.basename(self.processed_files["instrumental_shifted"])}, 音高: {self.instrumental_pitch_slider.value()}'
                })
            elif 'instrumental' in self.processed_files:
                output_tracks_data.append({
                    'id': 'instrumental',
                    'title': '④ 伴奏',
                    'filepath': self.processed_files['instrumental'],
                    'metadata': f'文件: {os.path.basename(self.processed_files["instrumental"])}'
                })

            # 和声 - 优先显示调整音高后的版本（如果存在）
            if 'harmony_shifted' in self.processed_files:
                output_tracks_data.append({
                    'id': 'harmony_shifted',
                    'title': '⑤ 和声(已调整音高)',
                    'filepath': self.processed_files['harmony_shifted'],
                    'metadata': f'文件: {os.path.basename(self.processed_files["harmony_shifted"])}, 音高: {self.instrumental_pitch_slider.value()}'
                })
            elif 'harmony' in self.processed_files:
                output_tracks_data.append({
                    'id': 'harmony',
                    'title': '⑤ 和声',
                    'filepath': self.processed_files['harmony'],
                    'metadata': f'文件: {os.path.basename(self.processed_files["harmony"])}'
                })

        if not output_tracks_data:
            self.initial_output_message_label.setText("没有输出结果。")
            self.initial_output_message_label.show()
            return

        for track_data in output_tracks_data:
            entry = OutputTrackEntryWidget(self.output_track_list_frame, track_data,
                                           self.handle_output_track_play,
                                           self.handle_output_track_download)
            self.output_track_list_layout.addWidget(entry)

        if output_tracks_data:
            self.handle_output_track_play(output_tracks_data[0])

    def handle_output_track_play(self, track_data):
        self.logger.info(f"播放音轨: {track_data['title']}")
        self.output_master_player.show()  # 播放时显示主播放器
        self.output_track_list_frame.show()  # 播放时显示分轨区

        # 获取文件路径
        filepath = track_data.get('filepath')

        # 检查文件是否存在
        if not os.path.exists(filepath):
            self.logger.error(f"文件不存在: {filepath}")
            QMessageBox.warning(self, "播放错误", f"文件不存在或无法访问: {filepath}")
            return

        # 在加载新音频前完全停止并重置当前媒体播放器
        self.output_master_player.media_player.stop()
        self.output_master_player.media_player.setMedia(QMediaContent())


        # 强制进行垃圾回收，释放文件句柄
        import gc
        gc.collect()

        # 确保最新文件已完全写入并可访问
        # 使用try文件打开测试是否有访问权限
        try:
            with open(filepath, 'rb') as f:
                # 只读取一点点数据来验证文件可访问
                f.read(1024)
        except Exception as e:
            self.logger.error(f"文件访问错误: {str(e)}")
            # 如果无法访问，等待200毫秒后再次尝试
            QTimer.singleShot(200, lambda: self.handle_output_track_play(track_data))
            return

        # 现在加载音频到播放器
        self.output_master_player.load_audio({
            'filepath': filepath,
            'title': track_data['title'],
            'waveform_text': f"[波形: {os.path.basename(filepath)}]",
            'metadata_text': track_data.get('metadata', '处理信息: N/A'),
            'id': track_data.get('id', '')  # 确保ID被正确传递
        })

        # 加载后立即开始播放
        self.output_master_player.media_player.play()

    def handle_output_track_download(self, track_data):
        self.logger.info(f"下载音轨: {track_data['title']} 从 {track_data['filepath']}")

        # 获取所选的输出格式
        output_format = self.output_format_combo.currentText().lower()

        # 构建默认文件名
        original_filename = os.path.basename(track_data['filepath'])
        name_without_ext = os.path.splitext(original_filename)[0]
        default_filename = f"{name_without_ext}.{output_format}"

        # 打开保存文件对话框
        save_filepath, _ = QFileDialog.getSaveFileName(
            self, f"保存 {track_data['title']}", default_filename,
            f"音频文件 (*.{output_format});;所有文件 (*.*)"
        )

        if save_filepath:
            try:
                # 检查扩展名是否匹配所选格式
                if not save_filepath.lower().endswith(f".{output_format}"):
                    save_filepath += f".{output_format}"

                # 如果输入和输出格式相同并且是wav，直接复制文件
                if output_format == "wav" and track_data['filepath'].lower().endswith(".wav"):
                    shutil.copy2(track_data['filepath'], save_filepath)
                    self.logger.info(f"文件已保存: {save_filepath}")
                else:
                    # 否则使用ffmpeg进行格式转换
                    self.status_label.setText("正在转换音频格式...")
                    self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_WARNING};")

                    # 构建ffmpeg命令
                    cmd = [
                        FFMPEG_PATH,
                        "-y",  # 覆盖输出文件
                        "-i", track_data['filepath'],  # 输入文件
                    ]

                    # 根据不同格式添加特定参数
                    if output_format == "mp3":
                        cmd.extend([
                            "-codec:a", "libmp3lame",
                            "-qscale:a", "2"  # 高质量MP3
                        ])
                    elif output_format == "wav":
                        cmd.extend([
                            "-codec:a", "pcm_s16le",  # 16位PCM
                        ])

                    # 添加输出文件路径
                    cmd.append(save_filepath)

                    # 执行命令
                    self.logger.info(f"执行转换命令: {' '.join(cmd)}")
                    # 添加creationflags参数以隐藏控制台窗口
                    creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        creationflags=creation_flags
                    )
                    _, stderr = process.communicate()

                    if process.returncode != 0:
                        error_msg = f"音频转换失败: {stderr}"
                        self.logger.error(error_msg)
                        raise Exception(error_msg)

                self.status_label.setText(f"文件已保存: {os.path.basename(save_filepath)}")
                self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_SUCCESS};")
            except Exception as e:
                self.logger.error(f"文件保存失败: {str(e)}")
                self.status_label.setText(f"文件保存失败: {str(e)}")
                self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_ERROR};")
                QMessageBox.critical(self, "保存错误", f"保存文件失败: {str(e)}")
        else:
            self.logger.info("下载已取消。")

    def add_reverb_dry(self):
        """为转换后的人声添加混响并混合伴奏和和声"""
        self.logger.info("开始添加混响和混合音轨")
        QTimer.singleShot(0, lambda: self.status_label.setText("添加混响和混合音轨中..."))

        converted_vocal_file = self.processed_files.get('converted_vocal')
        instrumental_file = self.processed_files.get('instrumental')
        harmony_file = self.processed_files.get('harmony')

        if not converted_vocal_file:
            error_msg = "未找到转换后的人声文件"
            self.logger.error(error_msg)
            raise Exception(error_msg)

        # 完整模式需要检查伴奏文件
        if self.processing_mode_combo.currentText() == "完整模式" and not instrumental_file:
            error_msg = "未找到伴奏文件"
            self.logger.error(error_msg)
            raise Exception(error_msg)

        # 获取用户选择的输出格式
        output_format = self.output_format_combo.currentText().lower()

        # 使用带时间戳的临时文件名，避免文件占用冲突
        timestamp = int(time.time())
        temp_mix_file = os.path.join(self.output_dir, f"{self.original_filename}-混缩-temp-{timestamp}.{output_format}")
        final_mix_file = os.path.join(self.output_dir, f"{self.original_filename}-混缩.{output_format}")

        try:
            # 读取人声音频
            vocal_audio = AudioSegment.from_file(converted_vocal_file)

            # 获取混响参数
            room_size = self.room_size_slider.value() / 100.0
            damping = self.damping_slider.value() / 100.0
            wet_level = self.wet_level_slider.value() / 100.0
            dry_level = self.dry_level_slider.value() / 100.0

            # 是否应用混响（根据复选框状态）
            apply_reverb = self.reverb_enabled_check.isChecked()

            # 应用混响效果（如果启用）
            if apply_reverb:
                self.logger.info("应用混响效果")
                vocal_audio_with_reverb = add_reverb(
                    vocal_audio,
                    room_size=room_size,
                    damping=damping,
                    wet_level=wet_level,
                    dry_level=dry_level
                )
            else:
                self.logger.info("未启用混响，使用原始人声")
                vocal_audio_with_reverb = vocal_audio

            # 获取用户设置的音量调整值（默认值，实际混音时会使用对话框中的值）
            # 这些值仅用于显示在UI中，不直接用于处理

            # 干声模式下，只导出带混响的人声
            if self.processing_mode_combo.currentText() == "干声模式":
                # 先保存到临时文件
                if output_format == "wav":
                    vocal_audio_with_reverb.export(temp_mix_file, format="wav")
                elif output_format == "mp3":
                    vocal_audio_with_reverb.export(temp_mix_file, format="mp3", bitrate="320k")
                # 移除了flac格式处理代码
                else:
                    # 默认使用WAV格式
                    temp_mix_file = os.path.join(self.output_dir, f"{self.original_filename}-混缩-temp-{timestamp}.wav")
                    vocal_audio_with_reverb.export(temp_mix_file, format="wav")

                self.logger.info(f"临时混响文件保存完成: {temp_mix_file}")

                # 处理文件占用问题
                try:
                    if os.path.exists(final_mix_file):
                        os.unlink(final_mix_file)
                        self.logger.info(f"已删除原混音文件: {final_mix_file}")
                except Exception as e:
                    self.logger.warning(f"无法删除原混音文件，将使用新文件名: {str(e)}")
                    # 如果删除失败，使用带时间戳的文件名作为最终名称
                    final_mix_file = temp_mix_file

                # 如果成功删除原文件，则重命名临时文件
                if final_mix_file != temp_mix_file:
                    try:
                        os.rename(temp_mix_file, final_mix_file)
                        self.logger.info(f"已将临时文件重命名为: {final_mix_file}")
                    except Exception as e:
                        self.logger.warning(f"重命名文件失败，将使用临时文件名: {str(e)}")
                        final_mix_file = temp_mix_file

                self.logger.info(f"混响添加完成: {final_mix_file}")
                self.processed_files['final_mix'] = final_mix_file
                return

            # 完整模式下，混合人声、伴奏和可选的和声
            # 确保文件路径正确
            if instrumental_file.startswith('temp/'):
                instrumental_file = os.path.join('uvr5', instrumental_file)
                self.logger.info(f"修正伴奏文件路径为: {instrumental_file}")

            if harmony_file and harmony_file.startswith('temp/'):
                harmony_file = os.path.join('uvr5', harmony_file)
                self.logger.info(f"修正和声文件路径为: {harmony_file}")

            # 读取伴奏音频
            instrumental_audio = AudioSegment.from_file(instrumental_file)

            # 调整伴奏音高（如果需要）
            instrumental_pitch_shift = self.instrumental_pitch_slider.value()
            if instrumental_pitch_shift != 0:
                self.logger.info(f"调整伴奏音高: {instrumental_pitch_shift} 半音")

                # 伴奏临时文件，用于调整音高
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_inst_file:
                    temp_inst_path = temp_inst_file.name

                # 构建音高调整命令（使用ffmpeg的rubberband）
                cmd = [
                    FFMPEG_PATH,
                    "-y",
                    "-i", instrumental_file,
                    "-filter:a", f"rubberband=pitch={2**(instrumental_pitch_shift/12)}",
                    temp_inst_path
                ]

                # 执行命令
                try:
                    # 使用辅助函数执行命令
                    returncode, _, stderr = run_subprocess(cmd, True, self.subprocess_list)

                    if returncode == 0:
                        # 成功，使用处理后的伴奏
                        instrumental_audio = AudioSegment.from_file(temp_inst_path)
                        self.logger.info("伴奏音高调整成功")
                    else:
                        self.logger.warning(f"伴奏音高调整失败: {stderr}")
                        # 失败则继续使用原始伴奏

                    # 删除临时文件
                    if os.path.exists(temp_inst_path):
                        os.unlink(temp_inst_path)

                except Exception as e:
                    self.logger.warning(f"伴奏音高调整过程出错: {str(e)}")
                    # 出错则继续使用原始伴奏

            # 处理和声文件（如果存在且需要加入和声）
            harmony_audio = None
            if self.harmony_to_accomp_check.isChecked() and harmony_file:
                self.logger.info("处理和声文件")
                harmony_audio = AudioSegment.from_file(harmony_file)

                # 和声也需要应用相同的音高调整
                if instrumental_pitch_shift != 0:
                    self.logger.info(f"调整和声音高: {instrumental_pitch_shift} 半音")

                    # 和声临时文件，用于调整音高
                    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_harmony_file:
                        temp_harmony_path = temp_harmony_file.name

                    # 构建音高调整命令（使用ffmpeg的rubberband）
                    cmd = [
                        FFMPEG_PATH,
                        "-y",
                        "-i", harmony_file,
                        "-filter:a", f"rubberband=pitch={2**(instrumental_pitch_shift/12)}",
                        temp_harmony_path
                    ]

                    # 执行命令
                    try:
                        # 使用辅助函数执行命令
                        returncode, _, stderr = run_subprocess(cmd, True, self.subprocess_list)

                        if returncode == 0:
                            # 成功，使用处理后的和声
                            harmony_audio = AudioSegment.from_file(temp_harmony_path)
                            self.logger.info("和声音高调整成功")
                        else:
                            self.logger.warning(f"和声音高调整失败: {stderr}")
                            # 失败则继续使用原始和声

                        # 删除临时文件
                        if os.path.exists(temp_harmony_path):
                            os.unlink(temp_harmony_path)

                    except Exception as e:
                        self.logger.warning(f"和声音高调整过程出错: {str(e)}")
                        # 出错则继续使用原始和声

            # 使用混音对话框中的默认设置进行混音
            # 调整音量 - 使用当前UI中设置的音量值
            vocal_vol_adjustment = -3  # 默认人声音量调整
            instrumental_vol_adjustment = -5  # 默认伴奏音量调整

            # 应用音量调整前记录音频信息
            self.logger.info(f"应用音量调整 - 人声: {vocal_vol_adjustment} dB, 伴奏: {instrumental_vol_adjustment} dB")
            self.logger.info(f"人声音频调整前响度: {vocal_audio_with_reverb.dBFS:.2f} dBFS")
            self.logger.info(f"伴奏音频调整前响度: {instrumental_audio.dBFS:.2f} dBFS")

            # 应用音量调整
            vocal_audio_with_reverb = vocal_audio_with_reverb + vocal_vol_adjustment
            instrumental_audio = instrumental_audio + instrumental_vol_adjustment

            # 记录调整后的音频信息
            self.logger.info(f"人声音频调整后响度: {vocal_audio_with_reverb.dBFS:.2f} dBFS")
            self.logger.info(f"伴奏音频调整后响度: {instrumental_audio.dBFS:.2f} dBFS")

            # 混合人声和伴奏
            mixed_audio = vocal_audio_with_reverb.overlay(instrumental_audio)

            # 如果启用了和声并且存在和声文件，则加入和声
            if self.harmony_to_accomp_check.isChecked() and harmony_audio:
                self.logger.info("加入和声到混音")
                harmony_audio = harmony_audio + instrumental_vol_adjustment  # 和声音量与伴奏音量一致
                mixed_audio = mixed_audio.overlay(harmony_audio)

            # 先保存到临时文件
            if output_format == "wav":
                mixed_audio.export(temp_mix_file, format="wav")
            elif output_format == "mp3":
                mixed_audio.export(temp_mix_file, format="mp3", bitrate="320k")
            else:
                # 默认使用WAV格式
                temp_mix_file = os.path.join(self.output_dir, f"{self.original_filename}-混缩-temp-{timestamp}.wav")
                mixed_audio.export(temp_mix_file, format="wav")

            self.logger.info(f"临时混音文件保存完成: {temp_mix_file}")

            # 处理文件占用问题
            try:
                if os.path.exists(final_mix_file):
                    os.unlink(final_mix_file)
                    self.logger.info(f"已删除原混音文件: {final_mix_file}")
            except Exception as e:
                self.logger.warning(f"无法删除原混音文件，将使用新文件名: {str(e)}")
                # 如果删除失败，使用带时间戳的文件名作为最终名称
                final_mix_file = temp_mix_file

            # 如果成功删除原文件，则重命名临时文件
            if final_mix_file != temp_mix_file:
                try:
                    os.rename(temp_mix_file, final_mix_file)
                    self.logger.info(f"已将临时文件重命名为: {final_mix_file}")
                except Exception as e:
                    self.logger.warning(f"重命名文件失败，将使用临时文件名: {str(e)}")
                    final_mix_file = temp_mix_file

            self.logger.info(f"混音添加完成: {final_mix_file}")

            # 保存最终混音文件路径
            self.processed_files['final_mix'] = final_mix_file

        except Exception as e:
            self.logger.error(f"添加混响和混合音轨过程中出错: {str(e)}")
            raise Exception(f"添加混响和混合音轨失败: {str(e)}")

    def on_processing_complete(self):
        """处理完成后的回调"""
        self.logger.info("处理完成")
        self.status_label.setText("处理完成!")
        self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_SUCCESS};")
        self.processing = False

        # 恢复按钮状态和文本
        self.process_button.setEnabled(True)
        if hasattr(self, 'process_button_original_text'):
            self.process_button.setText(self.process_button_original_text)
        else:
            self.process_button.setText("🚀 一键翻唱")  # 默认文本

        # 干声模式下不需要设置最终混音文件
        # 处理完成后直接显示转换后的人声

        # 处理完成后更新显示结果，并改变预览区域的样式使其更加突出
        self._display_processed_results()

        # 使用更鲜明的配色方案使预览区域更加突出
        highlight_color = "#3a5a94"  # 深蓝色调，更加突出但不刺眼

        # 更新播放器的样式
        self.output_master_player.setStyleSheet(f"""
            QFrame#DetailedAudioPlayerWidget {{
                background-color: {highlight_color};
                border-radius: 12px;
                border: 1px solid {DEFAULT_THEME_ACCENT};
            }}
        """)

        # 同时更新分轨列表的配色方案，使用相同的强调色
        self.output_track_list_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {highlight_color};
                border-radius: 12px;
                border: 1px solid {DEFAULT_THEME_ACCENT};
                padding: 8px;
            }}

            /* 确保子控件文本颜色清晰可见 */
            QLabel {{
                color: white;
            }}
        """)

        # 遍历所有分轨项，更新其背景色为透明，避免重叠显示问题
        for i in range(self.output_track_list_layout.count()):
            widget = self.output_track_list_layout.itemAt(i).widget()
            if isinstance(widget, OutputTrackEntryWidget):
                widget.setStyleSheet("""
                    QFrame#OutputTrackEntry {
                        background-color: transparent;
                        border: 1px solid rgba(255, 255, 255, 0.2);
                    }
                    QLabel {
                        color: white;
                    }
                """)

        # 使用延迟播放文件，确保文件系统操作完成
        # 获取第一个输出轨道并播放
        mode = self.processing_mode_combo.currentText()

        # 根据模式选择要播放的文件
        if mode == "完整模式" and 'final_mix' in self.processed_files:
            track_data = {
                'id': 'mix',
                'title': '① 最终混音',
                'filepath': self.processed_files['final_mix'],
                'metadata': f'处理模式: {mode}, 文件: {os.path.basename(self.processed_files["final_mix"])}'
            }
            # 使用100ms延迟确保文件已完全写入
            QTimer.singleShot(100, lambda: self.handle_output_track_play(track_data))

        # 刷新成品歌曲列表
        QTimer.singleShot(500, self.load_finished_songs)

    def show_process_error(self, error_message):
        """显示处理错误"""
        self.logger.error(f"处理错误: {error_message}")
        self.status_label.setText(f"错误: {error_message}")
        self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_ERROR};")
        self.processing = False

        # 恢复按钮状态和文本
        self.process_button.setEnabled(True)
        if hasattr(self, 'process_button_original_text'):
            self.process_button.setText(self.process_button_original_text)
        else:
            self.process_button.setText("🚀 一键翻唱")  # 默认文本

        # 显示错误对话框
        try:
            # 截取错误信息，避免过长
            short_error = error_message
            if len(short_error) > 200:
                short_error = short_error[:197] + "..."

            QMessageBox.critical(self, "处理错误", f"处理音频时发生错误:\n{short_error}")
        except Exception as e:
            self.logger.error(f"显示错误对话框时出错: {str(e)}")
            # 不在这里引发新异常

    def process_audio(self):
        """音频处理主函数，支持干声模式和完整模式"""
        try:
            # 重置停止标志和子进程列表
            self.should_stop = False
            self.subprocess_list = []

            mode = self.processing_mode_combo.currentText()

            # 清理之前的处理结果
            self.processed_files = {}

            # 获取原始音频文件名（不含扩展名）用于输出目录
            if self.selected_audio_filepath:
                self.original_filename = os.path.splitext(os.path.basename(self.selected_audio_filepath))[0]
                # 创建输出目录
                self.output_dir = os.path.join("outputs", self.original_filename)
                os.makedirs(self.output_dir, exist_ok=True)

                # 清空目录中的所有文件
                self.logger.info(f"检查输出目录: {self.output_dir}")
                if os.path.exists(self.output_dir) and os.path.isdir(self.output_dir) and os.listdir(self.output_dir):
                    self.logger.info(f"清空输出目录中的所有文件: {self.output_dir}")

                    # 停止所有正在播放的媒体（可能会占用文件）
                    if hasattr(self, 'output_master_player') and self.output_master_player:
                        self.output_master_player.media_player.stop()
                        self.output_master_player.media_player.setMedia(QMediaContent())

                    # 强制垃圾回收以释放文件句柄
                    import gc
                    gc.collect()

                    # 短暂暂停，让系统有时间释放文件句柄
                    time.sleep(0.2)

                    for item in os.listdir(self.output_dir):
                        item_path = os.path.join(self.output_dir, item)
                        try:
                            if os.path.isfile(item_path):
                                # 尝试删除文件
                                try:
                                    os.unlink(item_path)
                                    self.logger.info(f"删除文件: {item_path}")
                                except PermissionError:
                                    # 如果是权限错误（通常是文件被占用），尝试重命名策略
                                    self.logger.warning(f"文件 {item_path} 可能被占用，尝试重命名后删除")
                                    try:
                                        # 创建带时间戳的备份文件名
                                        backup_name = f"{item_path}.{int(time.time())}.bak"
                                        os.rename(item_path, backup_name)
                                        self.logger.info(f"已将 {item_path} 重命名为 {backup_name}")
                                        # 标记为稍后删除
                                    except Exception as rename_err:
                                        self.logger.warning(f"重命名文件 {item_path} 失败: {str(rename_err)}")
                            elif os.path.isdir(item_path):
                                try:
                                    shutil.rmtree(item_path)
                                    self.logger.info(f"删除目录: {item_path}")
                                except PermissionError:
                                    self.logger.warning(f"目录 {item_path} 无法删除，可能有文件被占用")
                        except Exception as e:
                            self.logger.warning(f"删除文件/目录 {item_path} 失败: {str(e)}")

                    # 再次检查目录中是否还有文件
                    remaining_files = os.listdir(self.output_dir)
                    if remaining_files:
                        self.logger.warning(f"无法完全清空目录，剩余 {len(remaining_files)} 个文件")
                    else:
                        self.logger.info(f"目录 {self.output_dir} 已成功清空")

            if mode == "完整模式":
                # 使用preset_infer_cli.py脚本进行音频分离
                self.logger.info("使用完整模式处理")
                self.process_audio_full_mode()

                # 如果设置了停止标志，提前返回
                if self.should_stop:
                    self.logger.info("用户请求停止处理，中断完整模式流程")
                    return

                # 音色转换
                self.convert_voice_dry()

                # 如果设置了停止标志，提前返回
                if self.should_stop:
                    self.logger.info("用户请求停止处理，中断完整模式流程")
                    return

                # 在完整模式下，始终执行混音步骤，无论是否启用混响，都需要执行混音
                self.add_reverb_dry()  # 该函数会根据reverb_enabled_check的状态决定是否添加混响

                # 如果设置了停止标志，提前返回
                if self.should_stop:
                    self.logger.info("用户请求停止处理，中断完整模式流程")
                    return

                # 移动分离的文件到输出目录
                self.move_separated_files()
            else:  # 干声模式
                self.logger.info("使用干声模式处理")
                # 直接转换音色
                self.convert_voice_dry()

                # 如果设置了停止标志，提前返回
                if self.should_stop:
                    self.logger.info("用户请求停止处理，中断干声模式流程")
                    return

                # 干声模式下不添加混响，无论复选框是否被勾选
                # 已在UI中禁用了混响复选框

            # 如果设置了停止标志，提前返回
            if self.should_stop:
                self.logger.info("用户请求停止处理，不显示处理结果")
                return

            # 保存处理信息，包括模型名称等
            process_info = {
                "model": self.model_combo.currentText(),
                "config": self.config_combo.currentText(),
                "vocal_pitch": self.vocal_pitch_slider.value(),
                "instrumental_pitch": self.instrumental_pitch_slider.value(),
                "processing_mode": self.processing_mode_combo.currentText(),
                "output_format": self.output_format_combo.currentText(),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            }

            # 保存到outputs目录下的process_info.json文件
            try:
                with open(os.path.join(self.output_dir, "process_info.json"), "w") as f:
                    json.dump(process_info, f, ensure_ascii=False, indent=2)
                self.logger.info(f"处理信息已保存到: {os.path.join(self.output_dir, 'process_info.json')}")
            except Exception as e:
                self.logger.warning(f"保存处理信息失败: {str(e)}")

            # 处理完成后更新UI
            QTimer.singleShot(0, self.on_processing_complete)

        except Exception as e:
            self.logger.error(f"处理过程中出错: {str(e)}")
            QTimer.singleShot(0, lambda: self.show_process_error(str(e)))

    def process_audio_full_mode(self):
        """完整模式下使用preset_infer_cli.py进行音频分离"""
        self.logger.info("完整模式 - 开始音频分离")
        QTimer.singleShot(0, lambda: self.status_label.setText("分离音频中..."))

        if not self.selected_audio_filepath:
            error_msg = "请先选择音频文件"
            self.logger.error(error_msg)
            raise Exception(error_msg)

        try:
            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            # 创建输入目录
            input_dir = os.path.join("input")
            os.makedirs(input_dir, exist_ok=True)

            # 清空input目录，防止先前的文件干扰
            self.logger.info("清空input目录")

            # 停止所有正在播放的媒体（可能会占用文件）
            if hasattr(self, 'output_master_player') and self.output_master_player:
                self.output_master_player.media_player.stop()
                self.output_master_player.media_player.setMedia(QMediaContent())

            # 强制垃圾回收以释放文件句柄
            import gc
            gc.collect()

            # 短暂暂停，让系统有时间释放文件句柄
            time.sleep(0.2)

            for item in os.listdir(input_dir):
                item_path = os.path.join(input_dir, item)
                try:
                    if os.path.isfile(item_path):
                        # 尝试删除文件
                        try:
                            os.unlink(item_path)
                            self.logger.info(f"删除input文件: {item_path}")
                        except PermissionError:
                            # 如果是权限错误（通常是文件被占用），尝试重命名策略
                            self.logger.warning(f"input文件 {item_path} 可能被占用，尝试重命名后删除")
                            try:
                                # 创建带时间戳的备份文件名
                                backup_name = f"{item_path}.{int(time.time())}.bak"
                                os.rename(item_path, backup_name)
                                self.logger.info(f"已将 {item_path} 重命名为 {backup_name}")
                            except Exception as rename_err:
                                self.logger.warning(f"重命名input文件 {item_path} 失败: {str(rename_err)}")
                    elif os.path.isdir(item_path):
                        try:
                            shutil.rmtree(item_path)
                            self.logger.info(f"删除input目录: {item_path}")
                        except PermissionError:
                            self.logger.warning(f"input目录 {item_path} 无法删除，可能有文件被占用")
                            # 尝试逐个删除目录内的文件
                            try:
                                for sub_item in os.listdir(item_path):
                                    sub_path = os.path.join(item_path, sub_item)
                                    if os.path.isfile(sub_path):
                                        try:
                                            os.unlink(sub_path)
                                        except:
                                            pass
                            except:
                                pass
                except Exception as e:
                    self.logger.warning(f"删除input文件/目录 {item_path} 失败: {str(e)}")

            # 再次检查目录中是否还有文件
            remaining_files = os.listdir(input_dir)
            if remaining_files:
                self.logger.warning(f"无法完全清空input目录，剩余 {len(remaining_files)} 个文件")
            else:
                self.logger.info(f"input目录已成功清空")

            # 清空results目录，防止文件混淆
            results_dir = os.path.join("results")
            if os.path.exists(results_dir):
                self.logger.info("清空results目录")
                # 删除目录下的所有文件，但保留目录本身

                # 停止所有正在播放的媒体（可能会占用文件）
                if hasattr(self, 'output_master_player') and self.output_master_player:
                    self.output_master_player.media_player.stop()
                    self.output_master_player.media_player.setMedia(QMediaContent())

                # 强制垃圾回收以释放文件句柄
                import gc
                gc.collect()

                # 短暂暂停，让系统有时间释放文件句柄
                time.sleep(0.2)

                for item in os.listdir(results_dir):
                    item_path = os.path.join(results_dir, item)
                    try:
                        if os.path.isfile(item_path):
                            # 尝试删除文件
                            try:
                                os.unlink(item_path)
                                self.logger.info(f"删除results文件: {item_path}")
                            except PermissionError:
                                # 如果是权限错误（通常是文件被占用），尝试重命名策略
                                self.logger.warning(f"results文件 {item_path} 可能被占用，尝试重命名后删除")
                                try:
                                    # 创建带时间戳的备份文件名
                                    backup_name = f"{item_path}.{int(time.time())}.bak"
                                    os.rename(item_path, backup_name)
                                    self.logger.info(f"已将 {item_path} 重命名为 {backup_name}")
                                except Exception as rename_err:
                                    self.logger.warning(f"重命名results文件 {item_path} 失败: {str(rename_err)}")
                        elif os.path.isdir(item_path):
                            try:
                                shutil.rmtree(item_path)
                                self.logger.info(f"删除results目录: {item_path}")
                            except PermissionError:
                                self.logger.warning(f"results目录 {item_path} 无法删除，可能有文件被占用")
                                # 尝试逐个删除目录内的文件
                                try:
                                    for sub_item in os.listdir(item_path):
                                        sub_path = os.path.join(item_path, sub_item)
                                        if os.path.isfile(sub_path):
                                            try:
                                                os.unlink(sub_path)
                                            except:
                                                pass
                                except:
                                    pass
                    except Exception as e:
                        self.logger.warning(f"删除results文件/目录 {item_path} 失败: {str(e)}")

                # 再次检查目录中是否还有文件
                remaining_files = os.listdir(results_dir)
                if remaining_files:
                    self.logger.warning(f"无法完全清空results目录，剩余 {len(remaining_files)} 个文件")
                else:
                    self.logger.info(f"results目录已成功清空")
            else:
                # 如果目录不存在，创建它
                os.makedirs(results_dir, exist_ok=True)

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            # 复制音频文件到input目录
            input_file = os.path.join(input_dir, os.path.basename(self.selected_audio_filepath))
            shutil.copy2(self.selected_audio_filepath, input_file)

            # 获取用户选择的输出格式
            output_format = self.output_format_combo.currentText().lower()

            # 使用相对路径的Python解释器
            python_exe = os.path.join(".", "workenv", "python.exe")

            # 构建preset_infer_cli.py的调用命令
            cmd = [
                python_exe, './msst/scripts/preset_infer_cli.py',
                '-p', './msst/preset.json',
                '-i', input_dir,
                '-o', 'results',
                '-f', output_format,
                '--extra_output_dir'
            ]

            self.logger.info(f"音频分离命令: {' '.join(cmd)}")

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            # 执行命令
            # 使用辅助函数执行命令
            returncode, _, stderr = run_subprocess(cmd, True, self.subprocess_list)

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            if returncode != 0:
                self.logger.error(f"音频分离失败: {stderr}")
                raise Exception(f"音频分离失败: {stderr}")

            # 检查生成的文件
            vocals_file = os.path.join("results", f"{self.original_filename}_vocals_karaoke_noreverb.{output_format}")
            instrumental_file = os.path.join("results", f"{self.original_filename}_other.{output_format}")
            instrumental_file_alt = os.path.join("results", "extra_output", f"{self.original_filename}_other.{output_format}")
            harmony_file = os.path.join("results", "extra_output", f"{self.original_filename}_vocals_other.{output_format}")

            # 优先检查extra_output目录中的伴奏文件
            if os.path.exists(instrumental_file_alt):
                instrumental_file = instrumental_file_alt
                self.logger.info(f"伴奏文件位于extra_output目录: {instrumental_file}")

            # 记录原始文件名，用于调试
            self.logger.info(f"查找人声文件: {vocals_file}")
            self.logger.info(f"查找伴奏文件: {instrumental_file}")
            self.logger.info(f"查找和声文件: {harmony_file}")

            # 列出results目录中实际生成的文件
            if os.path.exists("results"):
                self.logger.info("results目录中的文件:")
                for item in os.listdir("results"):
                    self.logger.info(f" - {item}")
                if os.path.exists(os.path.join("results", "extra_output")):
                    self.logger.info("results/extra_output目录中的文件:")
                    for item in os.listdir(os.path.join("results", "extra_output")):
                        self.logger.info(f" - {item}")

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            # 寻找替代文件（如果精确匹配的文件不存在）
            if not os.path.exists(vocals_file):
                # 查找alternative文件 - 通常是前缀匹配
                vocal_alternatives = [f for f in os.listdir("results") if
                                      f.startswith(self.original_filename) and
                                      "vocals" in f and
                                      f.endswith(f".{output_format}")]
                if vocal_alternatives:
                    vocals_file = os.path.join("results", vocal_alternatives[0])
                    self.logger.info(f"使用替代人声文件: {vocals_file}")

            if not os.path.exists(instrumental_file):
                # 查找alternative文件 - 查找任何包含"other"且不包含"vocals"的文件
                instr_alternatives = [f for f in os.listdir("results") if
                                     f.startswith(self.original_filename) and
                                     "other" in f and
                                     "vocals" not in f and
                                     f.endswith(f".{output_format}")]
                if instr_alternatives:
                    instrumental_file = os.path.join("results", instr_alternatives[0])
                    self.logger.info(f"使用替代伴奏文件: {instrumental_file}")
                # 如果results目录下没找到，再检查extra_output目录
                elif os.path.exists(os.path.join("results", "extra_output")):
                    instr_alternatives = [f for f in os.listdir(os.path.join("results", "extra_output")) if
                                         f.startswith(self.original_filename) and
                                         "other" in f and
                                         "vocals" not in f and
                                         f.endswith(f".{output_format}")]
                    if instr_alternatives:
                        instrumental_file = os.path.join("results", "extra_output", instr_alternatives[0])
                        self.logger.info(f"使用extra_output目录中的替代伴奏文件: {instrumental_file}")

            if not os.path.exists(harmony_file) and os.path.exists(os.path.join("results", "extra_output")):
                # 查找extra_output目录中的和声文件
                harmony_alternatives = [f for f in os.listdir(os.path.join("results", "extra_output")) if
                                       f.startswith(self.original_filename) and
                                       "vocals" in f and
                                       "other" in f and
                                       f.endswith(f".{output_format}")]
                if harmony_alternatives:
                    harmony_file = os.path.join("results", "extra_output", harmony_alternatives[0])
                    self.logger.info(f"使用替代和声文件: {harmony_file}")

            if not os.path.exists(vocals_file):
                self.logger.error("音频分离失败: 未找到人声文件")
                raise Exception("音频分离失败: 未找到人声文件")

            if not os.path.exists(instrumental_file):
                self.logger.error("音频分离失败: 未找到伴奏文件")
                raise Exception("音频分离失败: 未找到伴奏文件")

            # 保存分离文件路径
            self.processed_files['vocal'] = vocals_file
            self.processed_files['instrumental'] = instrumental_file
            if os.path.exists(harmony_file):
                self.processed_files['harmony'] = harmony_file
                self.logger.info(f"找到和声文件: {harmony_file}")

            self.logger.info("音频分离完成")

        except Exception as e:
            self.logger.error(f"完整模式音频分离过程中出错: {str(e)}")
            raise Exception(f"音频分离失败: {str(e)}")

    def move_separated_files(self):
        """将分离的文件移动到输出目录并重命名"""
        self.logger.info("开始移动分离的文件到输出目录")

        try:
            # 获取分离文件路径
            vocals_file = self.processed_files.get('vocal')
            instrumental_file = self.processed_files.get('instrumental')
            harmony_file = self.processed_files.get('harmony')

            # 构建目标文件路径
            vocals_output = os.path.join(self.output_dir, f"{self.original_filename}-人声.wav")
            instrumental_output = os.path.join(self.output_dir, f"{self.original_filename}-伴奏.wav")
            harmony_output = os.path.join(self.output_dir, f"{self.original_filename}-和声.wav")

            # 移动并重命名文件
            if vocals_file and os.path.exists(vocals_file):
                shutil.copy2(vocals_file, vocals_output)
                self.processed_files['original_vocal'] = vocals_output
                self.logger.info(f"人声文件已移动到: {vocals_output}")

            if instrumental_file and os.path.exists(instrumental_file):
                shutil.copy2(instrumental_file, instrumental_output)
                self.processed_files['instrumental'] = instrumental_output
                self.logger.info(f"伴奏文件已移动到: {instrumental_output}")

            if harmony_file and os.path.exists(harmony_file):
                shutil.copy2(harmony_file, harmony_output)
                self.processed_files['harmony'] = harmony_output
                self.logger.info(f"和声文件已移动到: {harmony_output}")

        except Exception as e:
            self.logger.error(f"移动分离文件过程中出错: {str(e)}")
            # 不中断处理流程，只记录错误

    def convert_voice_dry(self):
        """音色转换函数，支持干声模式和完整模式"""
        self.logger.info("开始转换音色")
        QTimer.singleShot(0, lambda: self.status_label.setText("转换音色中..."))

        # 获取用户选择的输出格式
        output_format = "wav"  # 转换阶段始终使用wav，最终混音时再考虑输出格式

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 获取音高调整值
        vocal_pitch_shift = self.vocal_pitch_slider.value()

        # 生成包含音高信息的文件名前缀
        def get_pitch_suffix(pitch):
            if pitch == 0:
                return ""
            elif pitch < 0:
                return f"降{abs(pitch)}"
            else:
                return f"升{pitch}"

        pitch_suffix = get_pitch_suffix(vocal_pitch_shift)

        # 转换后的输出文件路径（根据音高调整添加后缀）
        if pitch_suffix:
            converted_filename = f"{self.original_filename}{pitch_suffix}-转换人声.{output_format}"
        else:
            converted_filename = f"{self.original_filename}-转换人声.{output_format}"

        converted_vocal_file = os.path.join(self.output_dir, converted_filename)

        # 如果文件已存在，确保文件没有被占用
        if os.path.exists(converted_vocal_file):
            # 停止正在播放的音频
            if hasattr(self, 'output_master_player') and self.output_master_player:
                self.output_master_player.media_player.stop()
                self.output_master_player.media_player.setMedia(QMediaContent())

            # 强制垃圾回收
            import gc
            gc.collect()

            # 尝试删除文件
            try:
                # 等待100毫秒，确保文件句柄释放
                time.sleep(0.1)
                os.unlink(converted_vocal_file)
                self.logger.info(f"已删除旧的输出文件: {converted_vocal_file}")
            except Exception as e:
                self.logger.warning(f"无法删除现有文件，可能仍被占用: {str(e)}")
                # 使用带时间戳的临时文件名，避免文件占用冲突
                timestamp = int(time.time())
                converted_vocal_file = os.path.join(self.output_dir, f"{self.original_filename}{pitch_suffix}-转换人声-{timestamp}.{output_format}")
                self.logger.info(f"将使用新文件名: {converted_vocal_file}")

        try:
            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音色转换过程")
                return

            # 确定输入文件
            input_file = self.selected_audio_filepath
            if 'vocal' in self.processed_files:
                # 在完整模式下，使用分离出的人声
                input_file = self.processed_files['vocal']
                self.logger.info(f"使用分离的人声文件进行转换: {input_file}")

            # 构建main_reflow.py的调用参数（使用相对路径）
            model_path = os.path.join("models", self.model_combo.currentText())
            config_path = os.path.join("models", self.config_combo.currentText()) if self.config_combo.currentText() != "无配置文件" else None

            # 使用相对路径的Python解释器
            python_exe = os.path.join(".", "workenv", "python.exe")

            cmd = [
                python_exe, 'main_reflow.py',
                '-m', model_path
            ]

            # 添加配置参数
            if config_path:
                cmd.extend(['-c', config_path])

            # 添加其他参数
            cmd.extend([
                '-i', input_file,
                '-o', converted_vocal_file,
                '-id', str(1),  # 默认使用ID 1
                '-k', str(vocal_pitch_shift),  # 音高调整
                '-pe', self.f0_extractor_combo.currentText().split(' ')[0],  # 提取器
                '-step', self.infer_step_entry.text(),  # 采样步数
                '-method', self.method_combo.currentText()  # 采样方法
            ])

            # 根据声码器类型添加适当的参数
            selected_vocoder = self.vocoder_combo.currentText()
            if selected_vocoder in ['kouon_pc', 'pc_nsf_hifigan_testing']:
                # 使用声域偏移参数（-v）
                cmd.extend(['-v', str(self.formant_shift_slider.value())])
            else:
                # 使用共振峰偏移参数（-f）
                cmd.extend(['-f', str(self.formant_shift_slider.value())])

            # 设备选择参数 - 默认使用CUDA
            device_selection = self.device_combo.currentText()
            if device_selection == "CPU":
                cmd.extend(['-d', 'cpu'])
            else:
                cmd.extend(['-d', 'cuda'])  # 默认使用CUDA

            self.logger.info(f"音色转换命令: {' '.join(cmd)}")

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音色转换过程")
                return

            # 设置环境变量，禁用matplotlib GUI
            process_env = os.environ.copy()
            process_env['MPLBACKEND'] = 'Agg'  # 使用非交互式后端

            # 使用辅助函数执行命令
            returncode, _, stderr = run_subprocess(cmd, True, self.subprocess_list)

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音色转换过程")
                return

            if returncode != 0:
                self.logger.error(f"音色转换失败: {stderr}")
                raise Exception(f"音色转换失败: {stderr}")

            self.logger.info(f"音色转换完成: {converted_vocal_file}")
            self.processed_files['converted_vocal'] = converted_vocal_file

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音色转换过程")
                return

            # 如果有伴奏和和声文件，并且音高调整不为0，则也为它们创建音高调整的版本
            instrumental_pitch_shift = self.instrumental_pitch_slider.value()
            if 'instrumental' in self.processed_files:
                original_instrumental = self.processed_files['instrumental']

                # 创建伴奏文件包含音高信息的路径
                instrumental_pitch_suffix = get_pitch_suffix(instrumental_pitch_shift)

                if instrumental_pitch_shift != 0:
                    # 伴奏音高调整
                    shifted_instrumental_filename = f"{self.original_filename}{instrumental_pitch_suffix}-伴奏.{output_format}"
                    shifted_instrumental_file = os.path.join(self.output_dir, shifted_instrumental_filename)

                    self.logger.info(f"调整伴奏音高: {instrumental_pitch_shift} 半音")

                    # 构建音高调整命令（使用ffmpeg的rubberband）
                    cmd = [
                        FFMPEG_PATH,
                        "-y",
                        "-i", original_instrumental,
                        "-filter:a", f"rubberband=pitch={2**(instrumental_pitch_shift/12)}",
                        shifted_instrumental_file
                    ]

                    # 检查停止标志
                    if self.should_stop:
                        self.logger.info("检测到停止标志，中断音高调整过程")
                        return

                    # 使用辅助函数执行命令
                    returncode, _, stderr = run_subprocess(cmd, True, self.subprocess_list)

                    if returncode == 0:
                        self.logger.info(f"伴奏音高调整成功: {shifted_instrumental_file}")
                        # 添加到处理文件字典，但保留原始文件的引用
                        self.processed_files['instrumental_shifted'] = shifted_instrumental_file
                    else:
                        self.logger.warning(f"伴奏音高调整失败: {stderr}")
                else:
                    # 如果没有音高调整，则复制原始伴奏文件到输出目录
                    copied_instrumental_filename = f"{self.original_filename}-伴奏.{output_format}"
                    copied_instrumental_file = os.path.join(self.output_dir, copied_instrumental_filename)

                    self.logger.info(f"复制伴奏文件到输出目录: {copied_instrumental_file}")
                    shutil.copy2(original_instrumental, copied_instrumental_file)
                    self.processed_files['instrumental_shifted'] = copied_instrumental_file

                # 如果有和声文件，也处理它
                if 'harmony' in self.processed_files and self.harmony_to_accomp_check.isChecked():
                    original_harmony = self.processed_files['harmony']

                    if instrumental_pitch_shift != 0:
                        # 和声文件包含音高信息的路径
                        shifted_harmony_filename = f"{self.original_filename}{instrumental_pitch_suffix}-和声.{output_format}"
                        shifted_harmony_file = os.path.join(self.output_dir, shifted_harmony_filename)

                        self.logger.info(f"调整和声音高: {instrumental_pitch_shift} 半音")

                        # 构建音高调整命令
                        cmd = [
                            FFMPEG_PATH,
                            "-y",
                            "-i", original_harmony,
                            "-filter:a", f"rubberband=pitch={2**(instrumental_pitch_shift/12)}",
                            shifted_harmony_file
                        ]

                        # 检查停止标志
                        if self.should_stop:
                            self.logger.info("检测到停止标志，中断音高调整过程")
                            return

                        # 使用辅助函数执行命令
                        returncode, _, stderr = run_subprocess(cmd, True, self.subprocess_list)

                        if returncode == 0:
                            self.logger.info(f"和声音高调整成功: {shifted_harmony_file}")
                            # 添加到处理文件字典，但保留原始文件的引用
                            self.processed_files['harmony_shifted'] = shifted_harmony_file
                        else:
                            self.logger.warning(f"和声音高调整失败: {stderr}")
                    else:
                        # 如果没有音高调整，则复制原始和声文件到输出目录
                        copied_harmony_filename = f"{self.original_filename}-和声.{output_format}"
                        copied_harmony_file = os.path.join(self.output_dir, copied_harmony_filename)

                        self.logger.info(f"复制和声文件到输出目录: {copied_harmony_file}")
                        shutil.copy2(original_harmony, copied_harmony_file)
                        self.processed_files['harmony_shifted'] = copied_harmony_file

        except Exception as e:
            self.logger.error(f"音色转换过程中出错: {str(e)}")
            raise Exception(f"音色转换失败: {str(e)}")

    def apply_noise_gate(self, input_file, output_file):
        """对音频文件应用门限降噪（使用FFmpeg的滤镜实现降噪效果）
        参数参考：
        - 阈值: -35 dB
        - 攻击时间: 10 ms
        - 保持时间: 50 ms
        - 释放时间: 50 ms
        - 自动门: 开启

        注意：所有降噪方法都必须保持音频时长不变！
        """
        self.logger.info(f"开始对文件进行门限降噪处理: {input_file}")

        try:
            # 构建FFmpeg命令
            # 使用agate滤镜实现门限降噪，注意makeup参数必须在1-64范围内
            cmd = [
                FFMPEG_PATH,
                "-y",
                "-i", input_file,
                "-af", f"agate=threshold=-35dB:ratio=9:attack=0.01:release=0.05:makeup=1:detection=rms",
                output_file
            ]

            self.logger.info(f"门限降噪命令: {' '.join(cmd)}")

            # 使用辅助函数执行命令
            returncode, _, stderr = run_subprocess(cmd, True, self.subprocess_list)

            if returncode != 0:
                self.logger.error(f"门限降噪处理失败: {stderr}")
                # 尝试使用备用方案 - 使用简单的高低通滤波器组合
                self.logger.info("尝试使用备用门限降噪方案...")
                backup_cmd = [
                    FFMPEG_PATH,
                    "-y",
                    "-i", input_file,
                    "-af", "highpass=f=200,lowpass=f=8000,anlmdn",  # anlmdn是非局部均值降噪，不影响时长
                    output_file
                ]

                self.logger.info(f"备用门限降噪命令: {' '.join(backup_cmd)}")

                # 使用辅助函数执行备用命令
                backup_returncode, _, backup_stderr = run_subprocess(backup_cmd, True, self.subprocess_list)

                if backup_returncode != 0:
                    self.logger.error(f"备用门限降噪处理也失败: {backup_stderr}")

                    # 如果备用方案也失败，使用最基本的高低通滤波，确保时长不变
                    self.logger.info("尝试使用最简单的滤波器方案...")
                    final_cmd = [
                        FFMPEG_PATH,
                        "-y",
                        "-i", input_file,
                        "-af", "highpass=f=150,lowpass=f=8000,dynaudnorm=f=150:g=15",  # 动态音频标准化，保持时长
                        output_file
                    ]

                    self.logger.info(f"最终门限降噪命令: {' '.join(final_cmd)}")

                    # 使用辅助函数执行最终命令
                    final_returncode, _, final_stderr = run_subprocess(final_cmd, True, self.subprocess_list)

                    if final_returncode != 0:
                        self.logger.error(f"所有降噪方案均失败: {final_stderr}")

                        # 最后的备用方案：如果所有降噪方法都失败，简单复制原文件
                        self.logger.warning("所有降噪方法失败，简单复制原文件...")
                        shutil.copy2(input_file, output_file)
                        return True
                    else:
                        self.logger.info("使用简单滤波器方案降噪成功")
                        return True
                else:
                    self.logger.info("备用降噪方案成功")
                    return True

            self.logger.info(f"门限降噪处理完成: {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"门限降噪处理过程中出错: {str(e)}")
            # 如果发生异常，简单复制原文件，确保处理流程不中断
            try:
                shutil.copy2(input_file, output_file)
                self.logger.warning(f"降噪处理出错，复制原文件: {input_file} -> {output_file}")
                return True
            except Exception as copy_error:
                self.logger.error(f"复制原文件也失败: {str(copy_error)}")
                return False

    def process_audio_full_mode(self):
        """完整模式下使用preset_infer_cli.py进行音频分离"""
        self.logger.info("完整模式 - 开始音频分离")
        QTimer.singleShot(0, lambda: self.status_label.setText("分离音频中..."))

        if not self.selected_audio_filepath:
            error_msg = "请先选择音频文件"
            self.logger.error(error_msg)
            raise Exception(error_msg)

        try:
            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            # 创建输入目录
            input_dir = os.path.join("input")
            os.makedirs(input_dir, exist_ok=True)

            # 清空input目录，防止先前的文件干扰
            self.logger.info("清空input目录")

            # 停止所有正在播放的媒体（可能会占用文件）
            if hasattr(self, 'output_master_player') and self.output_master_player:
                self.output_master_player.media_player.stop()
                self.output_master_player.media_player.setMedia(QMediaContent())

            # 强制垃圾回收以释放文件句柄
            import gc
            gc.collect()

            # 短暂暂停，让系统有时间释放文件句柄
            time.sleep(0.2)

            for item in os.listdir(input_dir):
                item_path = os.path.join(input_dir, item)
                try:
                    if os.path.isfile(item_path):
                        # 尝试删除文件
                        try:
                            os.unlink(item_path)
                            self.logger.info(f"删除input文件: {item_path}")
                        except PermissionError:
                            # 如果是权限错误（通常是文件被占用），尝试重命名策略
                            self.logger.warning(f"input文件 {item_path} 可能被占用，尝试重命名后删除")
                            try:
                                # 创建带时间戳的备份文件名
                                backup_name = f"{item_path}.{int(time.time())}.bak"
                                os.rename(item_path, backup_name)
                                self.logger.info(f"已将 {item_path} 重命名为 {backup_name}")
                            except Exception as rename_err:
                                self.logger.warning(f"重命名input文件 {item_path} 失败: {str(rename_err)}")
                    elif os.path.isdir(item_path):
                        try:
                            shutil.rmtree(item_path)
                            self.logger.info(f"删除input目录: {item_path}")
                        except PermissionError:
                            self.logger.warning(f"input目录 {item_path} 无法删除，可能有文件被占用")
                            # 尝试逐个删除目录内的文件
                            try:
                                for sub_item in os.listdir(item_path):
                                    sub_path = os.path.join(item_path, sub_item)
                                    if os.path.isfile(sub_path):
                                        try:
                                            os.unlink(sub_path)
                                        except:
                                            pass
                            except:
                                pass
                except Exception as e:
                    self.logger.warning(f"删除input文件/目录 {item_path} 失败: {str(e)}")

            # 再次检查目录中是否还有文件
            remaining_files = os.listdir(input_dir)
            if remaining_files:
                self.logger.warning(f"无法完全清空input目录，剩余 {len(remaining_files)} 个文件")
            else:
                self.logger.info(f"input目录已成功清空")

            # 清空results目录，防止文件混淆
            results_dir = os.path.join("results")
            if os.path.exists(results_dir):
                self.logger.info("清空results目录")
                # 删除目录下的所有文件，但保留目录本身

                # 停止所有正在播放的媒体（可能会占用文件）
                if hasattr(self, 'output_master_player') and self.output_master_player:
                    self.output_master_player.media_player.stop()
                    self.output_master_player.media_player.setMedia(QMediaContent())

                # 强制垃圾回收以释放文件句柄
                import gc
                gc.collect()

                # 短暂暂停，让系统有时间释放文件句柄
                time.sleep(0.2)

                for item in os.listdir(results_dir):
                    item_path = os.path.join(results_dir, item)
                    try:
                        if os.path.isfile(item_path):
                            # 尝试删除文件
                            try:
                                os.unlink(item_path)
                                self.logger.info(f"删除results文件: {item_path}")
                            except PermissionError:
                                # 如果是权限错误（通常是文件被占用），尝试重命名策略
                                self.logger.warning(f"results文件 {item_path} 可能被占用，尝试重命名后删除")
                                try:
                                    # 创建带时间戳的备份文件名
                                    backup_name = f"{item_path}.{int(time.time())}.bak"
                                    os.rename(item_path, backup_name)
                                    self.logger.info(f"已将 {item_path} 重命名为 {backup_name}")
                                except Exception as rename_err:
                                    self.logger.warning(f"重命名results文件 {item_path} 失败: {str(rename_err)}")
                        elif os.path.isdir(item_path):
                            try:
                                shutil.rmtree(item_path)
                                self.logger.info(f"删除results目录: {item_path}")
                            except PermissionError:
                                self.logger.warning(f"results目录 {item_path} 无法删除，可能有文件被占用")
                                # 尝试逐个删除目录内的文件
                                try:
                                    for sub_item in os.listdir(item_path):
                                        sub_path = os.path.join(item_path, sub_item)
                                        if os.path.isfile(sub_path):
                                            try:
                                                os.unlink(sub_path)
                                            except:
                                                pass
                                except:
                                    pass
                    except Exception as e:
                        self.logger.warning(f"删除results文件/目录 {item_path} 失败: {str(e)}")

                # 再次检查目录中是否还有文件
                remaining_files = os.listdir(results_dir)
                if remaining_files:
                    self.logger.warning(f"无法完全清空results目录，剩余 {len(remaining_files)} 个文件")
                else:
                    self.logger.info(f"results目录已成功清空")
            else:
                # 如果目录不存在，创建它
                os.makedirs(results_dir, exist_ok=True)

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            # 复制音频文件到input目录
            input_file = os.path.join(input_dir, os.path.basename(self.selected_audio_filepath))
            shutil.copy2(self.selected_audio_filepath, input_file)

            # 获取用户选择的输出格式
            output_format = self.output_format_combo.currentText().lower()

            # 使用相对路径的Python解释器
            python_exe = os.path.join(".", "workenv", "python.exe")

            # 构建preset_infer_cli.py的调用命令
            cmd = [
                python_exe, './msst/scripts/preset_infer_cli.py',
                '-p', './msst/preset.json',
                '-i', input_dir,
                '-o', 'results',
                '-f', output_format,
                '--extra_output_dir'
            ]

            self.logger.info(f"音频分离命令: {' '.join(cmd)}")

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            # 使用辅助函数执行命令
            returncode, _, stderr = run_subprocess(cmd, True, self.subprocess_list)

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            if returncode != 0:
                self.logger.error(f"音频分离失败: {stderr}")
                raise Exception(f"音频分离失败: {stderr}")

            # 检查生成的文件
            vocals_file = os.path.join("results", f"{self.original_filename}_vocals_karaoke_noreverb.{output_format}")
            instrumental_file = os.path.join("results", f"{self.original_filename}_other.{output_format}")
            instrumental_file_alt = os.path.join("results", "extra_output", f"{self.original_filename}_other.{output_format}")
            harmony_file = os.path.join("results", "extra_output", f"{self.original_filename}_vocals_other.{output_format}")

            # 优先检查extra_output目录中的伴奏文件
            if os.path.exists(instrumental_file_alt):
                instrumental_file = instrumental_file_alt
                self.logger.info(f"伴奏文件位于extra_output目录: {instrumental_file}")

            # 记录原始文件名，用于调试
            self.logger.info(f"查找人声文件: {vocals_file}")
            self.logger.info(f"查找伴奏文件: {instrumental_file}")
            self.logger.info(f"查找和声文件: {harmony_file}")

            # 列出results目录中实际生成的文件
            if os.path.exists("results"):
                self.logger.info("results目录中的文件:")
                for item in os.listdir("results"):
                    self.logger.info(f" - {item}")
                if os.path.exists(os.path.join("results", "extra_output")):
                    self.logger.info("results/extra_output目录中的文件:")
                    for item in os.listdir(os.path.join("results", "extra_output")):
                        self.logger.info(f" - {item}")

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            # 寻找替代文件（如果精确匹配的文件不存在）
            if not os.path.exists(vocals_file):
                # 查找alternative文件 - 通常是前缀匹配
                vocal_alternatives = [f for f in os.listdir("results") if
                                      f.startswith(self.original_filename) and
                                      "vocals" in f and
                                      f.endswith(f".{output_format}")]
                if vocal_alternatives:
                    vocals_file = os.path.join("results", vocal_alternatives[0])
                    self.logger.info(f"使用替代人声文件: {vocals_file}")

            if not os.path.exists(instrumental_file):
                # 查找alternative文件 - 查找任何包含"other"且不包含"vocals"的文件
                instr_alternatives = [f for f in os.listdir("results") if
                                     f.startswith(self.original_filename) and
                                     "other" in f and
                                     "vocals" not in f and
                                     f.endswith(f".{output_format}")]
                if instr_alternatives:
                    instrumental_file = os.path.join("results", instr_alternatives[0])
                    self.logger.info(f"使用替代伴奏文件: {instrumental_file}")
                # 如果results目录下没找到，再检查extra_output目录
                elif os.path.exists(os.path.join("results", "extra_output")):
                    instr_alternatives = [f for f in os.listdir(os.path.join("results", "extra_output")) if
                                         f.startswith(self.original_filename) and
                                         "other" in f and
                                         "vocals" not in f and
                                         f.endswith(f".{output_format}")]
                    if instr_alternatives:
                        instrumental_file = os.path.join("results", "extra_output", instr_alternatives[0])
                        self.logger.info(f"使用extra_output目录中的替代伴奏文件: {instrumental_file}")

            if not os.path.exists(harmony_file) and os.path.exists(os.path.join("results", "extra_output")):
                # 查找extra_output目录中的和声文件
                harmony_alternatives = [f for f in os.listdir(os.path.join("results", "extra_output")) if
                                       f.startswith(self.original_filename) and
                                       "vocals" in f and
                                       "other" in f and
                                       f.endswith(f".{output_format}")]
                if harmony_alternatives:
                    harmony_file = os.path.join("results", "extra_output", harmony_alternatives[0])
                    self.logger.info(f"使用替代和声文件: {harmony_file}")

            if not os.path.exists(vocals_file):
                self.logger.error("音频分离失败: 未找到人声文件")
                raise Exception("音频分离失败: 未找到人声文件")

            if not os.path.exists(instrumental_file):
                self.logger.error("音频分离失败: 未找到伴奏文件")
                raise Exception("音频分离失败: 未找到伴奏文件")

            # 保存分离文件路径
            self.processed_files['original_vocal_raw'] = vocals_file  # 保存原始未降噪的人声文件路径
            self.processed_files['instrumental'] = instrumental_file
            if os.path.exists(harmony_file):
                self.processed_files['harmony'] = harmony_file
                self.logger.info(f"找到和声文件: {harmony_file}")

            # 检查停止标志
            if self.should_stop:
                self.logger.info("检测到停止标志，中断音频分离过程")
                return

            # 对人声文件进行门限降噪处理
            self.logger.info("开始对分离的人声进行门限降噪处理")
            QTimer.singleShot(0, lambda: self.status_label.setText("对人声进行降噪中..."))

            # 创建降噪后的人声文件路径
            denoised_vocals_file = os.path.join("results", f"{self.original_filename}_vocals_karaoke_noreverb_denoised.{output_format}")

            # 应用门限降噪
            if self.apply_noise_gate(vocals_file, denoised_vocals_file):
                self.logger.info(f"人声降噪成功，使用降噪后的人声文件: {denoised_vocals_file}")
                self.processed_files['vocal'] = denoised_vocals_file  # 使用降噪后的人声进行后续处理
            else:
                self.logger.warning("人声降噪失败，将使用原始未降噪的人声文件")
                self.processed_files['vocal'] = vocals_file  # 降噪失败时使用原始人声

            self.logger.info("音频分离完成")

        except Exception as e:
            self.logger.error(f"完整模式音频分离过程中出错: {str(e)}")
            raise Exception(f"音频分离失败: {str(e)}")

# 创建混音设置对话框类
class ReverbMixSettingsDialog(QDialog):
    def __init__(self, parent=None, room_size=0.3, damping=0.1, wet_level=0.2, dry_level=0.9, vocal_volume=0, instrumental_volume=0):
        super().__init__(parent)
        self.setWindowTitle("混音与混响设置")
        self.setMinimumWidth(400)
        # 移除窗口右上角的问号按钮
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {DEFAULT_THEME_CARD_BG};
                color: {DEFAULT_THEME_FOREGROUND};
                border-radius: 10px;
                border: 1px solid {DEFAULT_THEME_BORDER};
            }}
            QLabel {{
                color: {DEFAULT_THEME_FOREGROUND};
            }}
            QPushButton {{
                background-color: {DEFAULT_THEME_ACCENT};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {DEFAULT_THEME_ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: {DEFAULT_THEME_ACCENT_PRESSED};
            }}
            QPushButton#CancelButton {{
                background-color: {DEFAULT_THEME_SECONDARY_BG};
                color: {DEFAULT_THEME_FOREGROUND};
                border: 1px solid {DEFAULT_THEME_BORDER};
            }}
            QPushButton#CancelButton:hover {{
                background-color: {DEFAULT_THEME_CARD_BG};
            }}
        """)

        # 创建布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # 音量控制部分
        volume_group_label = QLabel("音轨音量")
        volume_group_label.setFont(QFont(FONT_FAMILY, TITLE_FONT_SIZE, QFont.Bold))
        main_layout.addWidget(volume_group_label)

        # 人声音量滑块
        vocal_volume_layout = QHBoxLayout()
        vocal_volume_label = QLabel("人声音量:")
        vocal_volume_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        vocal_volume_layout.addWidget(vocal_volume_label)

        self.vocal_volume_slider = QSlider(Qt.Horizontal)
        self.vocal_volume_slider.setRange(-20, 10)
        self.vocal_volume_slider.setValue(vocal_volume)
        vocal_volume_layout.addWidget(self.vocal_volume_slider, 1)

        self.vocal_volume_value_label = QLabel(f"{vocal_volume} dB")
        self.vocal_volume_value_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        self.vocal_volume_value_label.setFixedWidth(50)
        self.vocal_volume_value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        vocal_volume_layout.addWidget(self.vocal_volume_value_label)

        self.vocal_volume_slider.valueChanged.connect(
            lambda val: self.vocal_volume_value_label.setText(f"{val} dB"))

        main_layout.addLayout(vocal_volume_layout)

        # 伴奏音量滑块
        instrumental_volume_layout = QHBoxLayout()
        instrumental_volume_label = QLabel("伴奏音量:")
        instrumental_volume_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        instrumental_volume_layout.addWidget(instrumental_volume_label)

        self.instrumental_volume_slider = QSlider(Qt.Horizontal)
        self.instrumental_volume_slider.setRange(-20, 10)
        self.instrumental_volume_slider.setValue(instrumental_volume)
        instrumental_volume_layout.addWidget(self.instrumental_volume_slider, 1)

        self.instrumental_volume_value_label = QLabel(f"{instrumental_volume} dB")
        self.instrumental_volume_value_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        self.instrumental_volume_value_label.setFixedWidth(50)
        self.instrumental_volume_value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        instrumental_volume_layout.addWidget(self.instrumental_volume_value_label)

        self.instrumental_volume_slider.valueChanged.connect(
            lambda val: self.instrumental_volume_value_label.setText(f"{val} dB"))

        main_layout.addLayout(instrumental_volume_layout)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet(f"background-color: {DEFAULT_THEME_BORDER};")
        main_layout.addWidget(separator)

        # 混响参数部分
        reverb_group_label = QLabel("混响参数")
        reverb_group_label.setFont(QFont(FONT_FAMILY, TITLE_FONT_SIZE, QFont.Bold))
        main_layout.addWidget(reverb_group_label)

        # 创建混响参数滑块
        # 房间大小
        room_size_layout = QHBoxLayout()
        room_size_label = QLabel("房间大小:")
        room_size_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        room_size_layout.addWidget(room_size_label)

        self.room_size_slider = QSlider(Qt.Horizontal)
        self.room_size_slider.setRange(0, 100)
        self.room_size_slider.setValue(int(room_size * 100))
        room_size_layout.addWidget(self.room_size_slider, 1)

        self.room_size_value_label = QLabel(f"{room_size:.2f}")
        self.room_size_value_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        self.room_size_value_label.setFixedWidth(50)
        self.room_size_value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        room_size_layout.addWidget(self.room_size_value_label)

        self.room_size_slider.valueChanged.connect(
            lambda val: self.room_size_value_label.setText(f"{val/100:.2f}"))

        main_layout.addLayout(room_size_layout)

        # 阻尼
        damping_layout = QHBoxLayout()
        damping_label = QLabel("阻尼:")
        damping_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        damping_layout.addWidget(damping_label)

        self.damping_slider = QSlider(Qt.Horizontal)
        self.damping_slider.setRange(0, 100)
        self.damping_slider.setValue(int(damping * 100))
        damping_layout.addWidget(self.damping_slider, 1)

        self.damping_value_label = QLabel(f"{damping:.2f}")
        self.damping_value_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        self.damping_value_label.setFixedWidth(50)
        self.damping_value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        damping_layout.addWidget(self.damping_value_label)

        self.damping_slider.valueChanged.connect(
            lambda val: self.damping_value_label.setText(f"{val/100:.2f}"))

        main_layout.addLayout(damping_layout)

        # 湿润度
        wet_level_layout = QHBoxLayout()
        wet_level_label = QLabel("湿润度:")
        wet_level_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        wet_level_layout.addWidget(wet_level_label)

        self.wet_level_slider = QSlider(Qt.Horizontal)
        self.wet_level_slider.setRange(0, 100)
        self.wet_level_slider.setValue(int(wet_level * 100))
        wet_level_layout.addWidget(self.wet_level_slider, 1)

        self.wet_level_value_label = QLabel(f"{wet_level:.2f}")
        self.wet_level_value_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        self.wet_level_value_label.setFixedWidth(50)
        self.wet_level_value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        wet_level_layout.addWidget(self.wet_level_value_label)

        self.wet_level_slider.valueChanged.connect(
            lambda val: self.wet_level_value_label.setText(f"{val/100:.2f}"))

        main_layout.addLayout(wet_level_layout)

        # 干燥度
        dry_level_layout = QHBoxLayout()
        dry_level_label = QLabel("干燥度:")
        dry_level_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        dry_level_layout.addWidget(dry_level_label)

        self.dry_level_slider = QSlider(Qt.Horizontal)
        self.dry_level_slider.setRange(0, 100)
        self.dry_level_slider.setValue(int(dry_level * 100))
        dry_level_layout.addWidget(self.dry_level_slider, 1)

        self.dry_level_value_label = QLabel(f"{dry_level:.2f}")
        self.dry_level_value_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        self.dry_level_value_label.setFixedWidth(50)
        self.dry_level_value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        dry_level_layout.addWidget(self.dry_level_value_label)

        self.dry_level_slider.valueChanged.connect(
            lambda val: self.dry_level_value_label.setText(f"{val/100:.2f}"))

        main_layout.addLayout(dry_level_layout)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet(f"background-color: {DEFAULT_THEME_BORDER};")
        main_layout.addWidget(separator)

        # 和声选项
        harmony_layout = QHBoxLayout()
        self.harmony_to_accomp_check = QCheckBox("和声加入伴奏")
        self.harmony_to_accomp_check.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        harmony_layout.addWidget(self.harmony_to_accomp_check)
        harmony_layout.addStretch()
        main_layout.addLayout(harmony_layout)

        # 按钮部分
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        cancel_button = QPushButton("取消")
        cancel_button.setObjectName("CancelButton")
        cancel_button.clicked.connect(self.reject)

        remix_button = QPushButton("应用并重新混音")
        remix_button.clicked.connect(self.accept)

        button_layout.addWidget(cancel_button)
        button_layout.addWidget(remix_button)

        main_layout.addLayout(button_layout)

    def get_values(self):
        """获取所有参数值"""
        return {
            'vocal_volume': self.vocal_volume_slider.value(),
            'instrumental_volume': self.instrumental_volume_slider.value(),
            'room_size': self.room_size_slider.value() / 100.0,
            'damping': self.damping_slider.value() / 100.0,
            'wet_level': self.wet_level_slider.value() / 100.0,
            'dry_level': self.dry_level_slider.value() / 100.0,
            'harmony_to_accomp': self.harmony_to_accomp_check.isChecked()
        }

class FinishedSongEntryWidget(QFrame):
    def __init__(self, parent, song_data, play_action_callback, context_menu_callback):
        super().__init__(parent)
        self.song_data = song_data
        self.play_action_callback = play_action_callback
        self.context_menu_callback = context_menu_callback

        self.setObjectName("FinishedSongEntry")
        self.setCursor(Qt.PointingHandCursor)  # 鼠标指针形状改为手形

        # 设置样式
        self.setStyleSheet(f"""
            QFrame#FinishedSongEntry {{
                background-color: #2B3440;  /* 更深的背景色，与容器背景形成对比 */
                border-radius: 8px;
                border: 1px solid {DEFAULT_THEME_BORDER};
                padding: 8px;
                margin: 2px 0px;
            }}
            QFrame#FinishedSongEntry:hover {{
                background-color: #323C4B;  /* 悬停时更亮一点的背景色 */
                border: 1px solid {DEFAULT_THEME_ACCENT};
            }}
        """)

        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 8, 10, 8)
        layout.setSpacing(10)

        # 左侧信息布局
        info_layout = QVBoxLayout()
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(4)

        # 歌曲名称
        self.title_label = QLabel(song_data.get('name', 'N/A'))
        self.title_label.setFont(QFont(FONT_FAMILY, LABEL_FONT_SIZE + 1, QFont.Bold))
        self.title_label.setStyleSheet(f"color: {DEFAULT_THEME_FOREGROUND};")
        info_layout.addWidget(self.title_label)

        # 创建时间和模型信息
        metadata_text = f"创建: {song_data.get('created_time', 'N/A')}"
        if song_data.get('model'):
            metadata_text += f" · 模型: {song_data.get('model')}"

        self.metadata_label = QLabel(metadata_text)
        self.metadata_label.setFont(QFont(FONT_FAMILY, CONTROL_FONT_SIZE))
        self.metadata_label.setStyleSheet(f"color: {DEFAULT_THEME_SECONDARY_TEXT};")
        info_layout.addWidget(self.metadata_label)

        layout.addLayout(info_layout, 1)  # 信息部分占据大部分空间

        # 三点菜单按钮
        self.menu_button = QPushButton("⋮")  # 竖直的三点
        self.menu_button.setFont(QFont(FONT_FAMILY, PLACEHOLDER_ICON_FONT_SIZE))
        self.menu_button.setObjectName("SongMenuButton")
        self.menu_button.setFixedSize(32, 32)
        self.menu_button.setCursor(Qt.PointingHandCursor)
        self.menu_button.setStyleSheet(f"""
            QPushButton#SongMenuButton {{
                background-color: transparent;
                color: {DEFAULT_THEME_SECONDARY_TEXT};
                border: none;
                border-radius: 16px;
                padding: 0px;
                font-weight: bold;
            }}
            QPushButton#SongMenuButton:hover {{
                background-color: {DEFAULT_THEME_BORDER};
                color: {DEFAULT_THEME_FOREGROUND};
            }}
        """)
        self.menu_button.clicked.connect(self._show_context_menu)
        layout.addWidget(self.menu_button)

        self.setLayout(layout)

        # 添加点击事件
        self.mousePressEvent = self._on_widget_clicked

    def _on_widget_clicked(self, event):
        """处理整个条目点击事件"""
        if event.button() == Qt.LeftButton:
            # 如果点击的是菜单按钮区域，不触发播放
            menu_button_rect = self.menu_button.geometry()
            if not menu_button_rect.contains(event.pos()):
                self.play_action_callback(self.song_data)
        super().mousePressEvent(event)

    def _show_context_menu(self):
        """显示上下文菜单"""
        # 获取菜单按钮的全局位置和大小
        button_pos = self.menu_button.mapToGlobal(QPoint(0, self.menu_button.height()))
        # 确保菜单显示在按钮下方
        self.context_menu_callback(self.song_data, button_pos)

# 激活对话框类
class ActivationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("软件激活")
        self.setMinimumWidth(400)
        self.setMinimumHeight(200)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

        # 创建UI
        self.setup_ui()

    def setup_ui(self):
        # 使用垂直布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 提示标签
        info_label = QLabel("请输入激活码以继续使用软件。")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 激活码输入区域
        activation_code_frame = QFrame()
        activation_code_layout = QVBoxLayout(activation_code_frame)
        activation_code_layout.setContentsMargins(0, 0, 0, 0)
        activation_code_layout.setSpacing(8)

        activation_code_label = QLabel("输入激活码:")
        activation_code_label.setStyleSheet("font-weight: bold;")
        activation_code_layout.addWidget(activation_code_label)

        self.activation_code_edit = QLineEdit()
        self.activation_code_edit.setFont(QFont(FONT_FAMILY, 12, QFont.Bold))
        self.activation_code_edit.setAlignment(Qt.AlignCenter)
        self.activation_code_edit.setPlaceholderText("请输入激活码")
        activation_code_layout.addWidget(self.activation_code_edit)

        layout.addWidget(activation_code_frame)

        # 状态信息标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_ERROR};")
        self.status_label.setWordWrap(True)
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)

        # 添加弹性空间
        layout.addStretch(1)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        # 添加弹性空间使按钮靠右对齐
        button_layout.addStretch(1)

        self.cancel_button = QPushButton("退出")
        self.cancel_button.setFixedWidth(100)
        self.cancel_button.clicked.connect(self.reject)

        self.activate_button = QPushButton("激活")
        self.activate_button.setFixedWidth(100)
        self.activate_button.clicked.connect(self.activate_software)
        self.activate_button.setDefault(True)  # 设为默认按钮

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.activate_button)

        layout.addLayout(button_layout)

    def activate_software(self):
        """验证激活码并激活软件"""
        activation_code = self.activation_code_edit.text().strip()

        if not activation_code:
            self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_ERROR};")
            self.status_label.setText("请输入激活码")
            return

        # 禁用激活按钮，防止重复点击
        self.activate_button.setEnabled(False)
        self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_WARNING};")
        self.status_label.setText("正在验证激活码...")

        # 使用QTimer进行短延迟，确保UI更新
        QTimer.singleShot(200, lambda: self._perform_activation(activation_code))

    def _perform_activation(self, activation_code):
        """执行激活流程"""
        try:
            # 验证激活码
            if activation_utils.verify_activation_code(activation_code):
                # 保存激活数据
                activation_data = {
                    "activation_time": int(time.time()),
                    "version": activation_utils.APP_ID
                }

                if activation_utils.save_activation_data(activation_data):
                    self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_SUCCESS};")
                    self.status_label.setText("激活成功！")

                    # 短暂延迟后关闭对话框
                    QTimer.singleShot(1500, self.accept)
                else:
                    self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_ERROR};")
                    self.status_label.setText("保存激活数据失败，请检查应用权限")
                    self.activate_button.setEnabled(True)
            else:
                self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_ERROR};")
                self.status_label.setText("激活码无效，请检查输入是否正确")
                self.activate_button.setEnabled(True)
        except Exception as e:
            self.status_label.setStyleSheet(f"color: {DEFAULT_THEME_ERROR};")
            self.status_label.setText(f"激活过程中出错: {str(e)}")
            self.activate_button.setEnabled(True)

if __name__ == "__main__":
    if hasattr(Qt, 'AA_EnableHighDpiScaling'): QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'): QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)
    main_window = App()
    main_window.show()
    sys.exit(app.exec_())