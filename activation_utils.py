import os
import sys
import json
import hashlib
import base64
import time
import logging
from typing import Dict, Optional

# 设置日志记录
logger = logging.getLogger("Activation")

# 应用标识符
APP_ID = "MuOuAISinging_v1.0"

def _get_activation_data_path() -> str:
    """获取激活数据文件路径"""
    if sys.platform == "win32":
        app_data = os.environ.get("LOCALAPPDATA", os.environ.get("APPDATA", ""))
        if not app_data:
            app_data = os.path.expanduser("~")
    else:
        app_data = os.path.expanduser("~")

    app_dir = os.path.join(app_data, ".muoucfg")
    os.makedirs(app_dir, exist_ok=True)

    file_name = "app_settings.dat"
    return os.path.join(app_dir, file_name)

def verify_activation_code(activation_code: str) -> bool:
    """验证激活码是否有效"""
    try:
        # 规范化输入，移除空格和连字符
        activation_code = activation_code.replace("-", "").replace(" ", "").upper()

        # 1. 验证激活码长度
        if len(activation_code) != 16:  # 激活码必须是16个字符
            logger.warning("激活码长度不正确")
            return False

        # 2. 验证激活码格式
        if not activation_code.isalnum():  # 只允许字母和数字
            logger.warning("激活码格式不正确")
            return False

        # 3. 验证激活码的校验和
        # 取前15个字符计算校验和
        code_body = activation_code[:15]
        checksum = sum(ord(c) for c in code_body) % 16
        expected_checksum = int(activation_code[-1], 16)

        if checksum != expected_checksum:
            logger.warning("激活码校验和不匹配")
            return False

        # 4. 验证激活码的特征模式
        # 前4个字符必须是APP_ID的某种变形
        app_hash = hashlib.md5(APP_ID.encode()).hexdigest()[:4].upper()
        if activation_code[:4] != app_hash:
            logger.warning("激活码特征模式不匹配")
            return False

        # 如果所有检查都通过，则激活码有效
        return True

    except Exception as e:
        logger.error(f"验证激活码时出错: {str(e)}")
        return False

def save_activation_data(activation_data: Dict) -> bool:
    """保存激活数据到文件"""
    try:
        # 准备要存储的数据
        data_to_store = {
            "activation_time": activation_data.get("activation_time", int(time.time())),
            "version": activation_data.get("version", APP_ID),
            "is_activated": True
        }

        # 将数据转换为JSON并进行简单编码
        json_data = json.dumps(data_to_store)
        encoded_data = base64.b64encode(json_data.encode()).decode()

        # 保存到文件
        file_path = _get_activation_data_path()
        with open(file_path, "w") as f:
            f.write(encoded_data)

        return True
    except Exception as e:
        logger.error(f"保存激活数据时出错: {str(e)}")
        return False

def load_activation_data() -> Optional[Dict]:
    """从文件加载激活数据"""
    try:
        file_path = _get_activation_data_path()

        if not os.path.exists(file_path):
            return None

        with open(file_path, "r") as f:
            encoded_data = f.read()

        # 解码数据
        json_data = base64.b64decode(encoded_data.encode()).decode()
        activation_data = json.loads(json_data)

        return activation_data
    except Exception as e:
        logger.error(f"加载激活数据时出错: {str(e)}")
        return None

def is_activated() -> bool:
    """检查应用是否已激活"""
    try:
        activation_data = load_activation_data()
        if not activation_data:
            logger.info("未找到激活数据")
            return False

        # 验证版本信息
        stored_version = activation_data.get("version")
        if stored_version != APP_ID:
            logger.warning(f"激活数据版本不匹配: 期望 {APP_ID}，实际 {stored_version}")
            return False

        # 检查激活状态
        is_activated = activation_data.get("is_activated", False)
        if not is_activated:
            logger.info("应用未激活")
            return False

        logger.info("应用已成功激活")
        return True
    except Exception as e:
        logger.error(f"检查激活状态时出错: {str(e)}")
        return False