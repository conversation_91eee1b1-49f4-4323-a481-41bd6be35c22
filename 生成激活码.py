#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
激活码生成工具 - 仅供开发者使用

此工具用于生成软件激活码。
生成的激活码格式为：XXXX-XXXX-XXXX-XXXX（16位字符，每4位用连字符分隔）
"""

import sys
import os
import hashlib
import argparse
import random
import string
from typing import Optional

# 应用ID - 与activation_utils.py中的APP_ID保持一致
APP_ID = "MuOuAISinging_v1.0"

def generate_activation_code() -> str:
    """
    生成激活码
    
    生成规则：
    1. 前4位：APP_ID的MD5哈希的前4位
    2. 中间11位：随机字母和数字
    3. 最后1位：前15位的校验和（0-F）
    """
    try:
        # 1. 生成前缀（基于APP_ID）
        prefix = hashlib.md5(APP_ID.encode()).hexdigest()[:4].upper()

        # 2. 生成11位随机字符（字母和数字）
        chars = string.ascii_uppercase + string.digits
        random_part = ''.join(random.choice(chars) for _ in range(11))

        # 3. 组合前15位
        code_body = prefix + random_part

        # 4. 计算校验和（前15位字符的ASCII码之和模16）
        checksum = sum(ord(c) for c in code_body) % 16
        checksum_char = hex(checksum)[2:].upper()

        # 5. 组合完整激活码
        activation_code = code_body + checksum_char

        # 6. 格式化为4组，每组4个字符
        formatted_code = '-'.join([activation_code[i:i+4] for i in range(0, len(activation_code), 4)])

        return formatted_code

    except Exception as e:
        print(f"生成激活码时出错: {str(e)}")
        return None

def main():
    """主程序"""
    parser = argparse.ArgumentParser(description="激活码生成工具")
    parser.add_argument("-n", "--number", type=int, default=1, help="要生成的激活码数量")
    args = parser.parse_args()

    print("============= 激活码生成工具 =============")
    print(f"将生成 {args.number} 个激活码\n")

    # 生成指定数量的激活码
    for i in range(args.number):
        activation_code = generate_activation_code()
        if activation_code:
            print(f"激活码 {i+1}: {activation_code}")
        else:
            print(f"生成第 {i+1} 个激活码时失败")

    print("\n生成完成。")

if __name__ == "__main__":
    main()