@echo off
chcp 65001

echo 正在开始打包木偶AI翻唱应用(轻量版)...
echo 使用Python: g:/0_Software/DDSP/DDSP6.3/workenv/python.exe
echo.
echo 注意: 此打包方式将创建一个轻量级exe文件，它依赖于workenv环境
echo 最终exe文件体积将从3GB减小到几十MB
echo.

:: 设置环境变量，使matplotlib使用非交互式后端
set MPLBACKEND=Agg

:: 更新核心依赖包
echo 正在更新关键依赖包...
"g:/0_Software/DDSP/DDSP6.3/workenv/python.exe" -m pip install --upgrade pyinstaller importlib_metadata

:: 运行打包脚本
echo 开始运行打包脚本...
"g:/0_Software/DDSP/DDSP6.3/workenv/python.exe" build_exe.py

:: 检查是否打包成功
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo 打包过程中出现错误！
    echo 错误代码: %ERRORLEVEL%
    echo 请检查上面的错误消息，修复问题后重试。
) else (
    echo.
    echo 打包过程完成！
    echo 可执行文件位于dist目录中的木偶AI翻唱.exe
    echo.
    echo 使用说明:
    echo 1. 此exe文件是轻量级的，必须与workenv文件夹一起使用
    echo 2. 请将dist目录中的木偶AI翻唱.exe复制到包含workenv文件夹的目录中
    echo 3. 确保保留以下文件和文件夹结构:
    echo    - 木偶AI翻唱.exe
    echo    - workenv/（Python环境文件夹）
    echo    - assets/（资源文件夹）
    echo    - ffmpeg/（音频处理工具）
)

echo.
pause