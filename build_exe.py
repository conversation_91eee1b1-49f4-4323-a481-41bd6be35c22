import os
import sys
import shutil
import traceback
import PyInstaller.__main__

# 此脚本用于打包一个轻量级的exe文件，它依赖于workenv环境中的Python解释器和依赖库
# 打包的exe文件只包含app.py直接使用的必要库，其他依赖将从workenv环境中加载
# 这样可以大幅减小exe文件的体积，从3GB减小到几十MB

def build_executable():
    try:
        print("开始打包应用程序...")
        
        # 输出Python版本和PyInstaller版本信息，用于调试
        print(f"Python版本: {sys.version}")
        print(f"PyInstaller版本: {PyInstaller.__version__}")
        print(f"执行路径: {os.getcwd()}")
        
        # 创建dist和build目录（如果不存在）
        os.makedirs("dist", exist_ok=True)
        os.makedirs("build", exist_ok=True)
        
        # 清理之前的构建和临时文件
        print("清理之前的构建文件和临时文件...")
        # 清理dist目录
        if os.path.exists("dist"):
            for item in os.listdir("dist"):
                item_path = os.path.join("dist", item)
                try:
                    if os.path.isfile(item_path):
                        os.unlink(item_path)
                    elif os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                except Exception as e:
                    print(f"清理文件失败: {e}")
        
        # 清理build目录
        if os.path.exists("build"):
            try:
                shutil.rmtree("build")
                os.makedirs("build", exist_ok=True)
                print("已清理build目录")
            except Exception as e:
                print(f"清理build目录失败: {e}")
                
        # 清理临时文件
        temp_dirs = ["__pycache__", "temp", ".pytest_cache"]
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    print(f"已清理临时目录: {temp_dir}")
                except Exception as e:
                    print(f"清理临时目录 {temp_dir} 失败: {e}")
                    
        # 清理msst模块中的临时文件
        msst_temp_path = os.path.join("msst", "temp")
        if os.path.exists(msst_temp_path):
            try:
                shutil.rmtree(msst_temp_path)
                print(f"已清理MSST临时目录")
            except Exception as e:
                print(f"清理MSST临时目录失败: {e}")
        
        # 需要包含的额外文件夹和文件 - 只包含app.py直接需要的资源
        additional_datas = [
            ("assets", "assets"),  # UI资源
            ("ffmpeg", "ffmpeg"),  # 音频处理需要
            ("activation_utils.py", "."),  # 激活相关功能
        ]
        
        # 不再自动添加所有Python文件，只添加app.py直接需要的资源
        # 其他Python模块将通过workenv环境提供
        
        # 把额外的文件和文件夹转换为pyinstaller的--add-data格式
        separator = ';' if sys.platform == 'win32' else ':'
        add_data_args = []
        for src, dst in additional_datas:
            if os.path.exists(src):
                add_data_args.append(f'--add-data={src}{separator}{dst}')
            else:
                print(f"警告: 资源文件/文件夹 '{src}' 不存在，将被跳过")
        
        # 设置打包参数 - 只包含app.py直接使用的必要库
        pyinstaller_args = [
            '--name=木偶AI翻唱',
            '--onefile',  # 打包成单个exe文件
            '--noconsole',  # 不显示控制台窗口
            '--windowed',  # 禁用所有控制台窗口，包括子进程
            '--icon=assets/images/title.ico',  # 应用图标
            '--clean',  # 清理PyInstaller缓存
            '--strip',  # 减小二进制文件大小
            '--noupx',  # 不使用UPX压缩，因为可能导致某些问题
            # 排除所有大型依赖，因为它们已经在workenv环境中
            '--exclude-module=torch',
            '--exclude-module=torchvision',
            '--exclude-module=torchaudio',
            '--exclude-module=tensorflow',
            '--exclude-module=matplotlib',
            '--exclude-module=scipy',
            '--exclude-module=sklearn',
            '--exclude-module=pandas',
            '--exclude-module=numba',
            '--exclude-module=llvmlite',
            '--exclude-module=librosa',
            '--exclude-module=soundfile',
            '--exclude-module=IPython',
            '--exclude-module=jupyter',
            '--exclude-module=notebook',
            '--exclude-module=black',
            '--exclude-module=jedi',
            '--exclude-module=parso',
            '--exclude-module=pytest',
            '--exclude-module=transformers',
            '--exclude-module=huggingface_hub',
            '--exclude-module=tokenizers',
            '--exclude-module=fairseq',
            '--exclude-module=gradio',
            '--exclude-module=asteroid',
            '--exclude-module=demucs',
            '--exclude-module=openunmix',
            '--exclude-module=pedalboard',
            '--exclude-module=resampy',
            '--exclude-module=audiomentations',
            '--exclude-module=torchcrepe',
            '--exclude-module=torchfcpe',
            '--exclude-module=pyworld',
            '--exclude-module=praat_parselmouth',
            '--exclude-module=cli_processor',  # 排除CLI处理器
            '--exclude-module=slicer',  # 排除音频切片器
            '--exclude-module=ddsp',  # 排除DDSP模块
            '--exclude-module=reflow',  # 排除reflow模块
            '--exclude-module=nsf_hifigan',  # 排除nsf_hifigan模块
            '--exclude-module=asp',  # 排除asp模块
            # 只包含app.py直接使用的必要库
            '--collect-all=PyQt5',  # 确保收集所有PyQt5相关模块
            '--collect-submodules=PyQt5.QtCore',
            '--collect-submodules=PyQt5.QtGui',
            '--collect-submodules=PyQt5.QtWidgets',
            '--collect-submodules=PyQt5.QtMultimedia',
            '--collect-all=pyqtgraph',  # 波形显示需要
            '--collect-all=numpy',  # 数据处理需要
            '--collect-all=pydub',  # 音频处理需要
            '--collect-all=PIL',  # 图像处理需要
            # 添加隐藏导入，明确告诉PyInstaller哪些模块是必需的
            '--hidden-import=PyQt5.sip',
            '--hidden-import=numpy.core._dtype_ctypes',
            '--hidden-import=PIL._tkinter_finder',
            '--hidden-import=wmi',  # 激活系统需要的硬件信息库
            '--hidden-import=cryptography',  # 激活数据加密所需
            '--add-data=assets;assets',  # 添加assets文件夹
            'app.py'  # 主脚本文件
        ]
        
        # 输出最终的PyInstaller命令
        print("PyInstaller命令行参数:")
        for arg in pyinstaller_args:
            print(f"  {arg}")
        
        print("\n正在执行PyInstaller打包...")
        try:
            # 执行打包命令
            PyInstaller.__main__.run(pyinstaller_args + add_data_args)
            
            # 检查是否生成了exe文件
            exe_path = os.path.join("dist", "木偶AI翻唱.exe")
            if os.path.exists(exe_path):
                print(f"\n打包成功！生成的可执行文件位于: {exe_path}")
                print(f"文件大小: {os.path.getsize(exe_path) / (1024*1024):.2f} MB")
                print("\n重要说明:")
                print("1. 此exe文件是轻量级的，它依赖于workenv环境中的Python解释器和依赖库")
                print("2. 请确保exe文件与workenv文件夹放在同一目录下")
                print("3. 此打包方式大幅减小了exe文件的体积，从3GB减小到几十MB")
            else:
                print("\n打包过程完成，但未找到可执行文件。请检查上面的日志输出查找错误。")
                
            # 打包后清理临时文件
            print("\n清理打包过程中生成的临时文件...")
            # 清理spec文件
            spec_file = "木偶AI翻唱.spec"
            if os.path.exists(spec_file):
                try:
                    os.remove(spec_file)
                    print(f"已删除spec文件: {spec_file}")
                except Exception as e:
                    print(f"删除spec文件失败: {e}")
            
            # 清理__pycache__目录
            for root, dirs, files in os.walk("."):
                for dir_name in dirs:
                    if dir_name == "__pycache__":
                        pycache_path = os.path.join(root, dir_name)
                        try:
                            shutil.rmtree(pycache_path)
                            print(f"已清理: {pycache_path}")
                        except Exception as e:
                            print(f"清理 {pycache_path} 失败: {e}")
            
        except Exception as e:
            print(f"打包过程中发生错误: {str(e)}")
            raise
        
        return True
    except Exception as e:
        print(f"\n打包过程中出现错误: {str(e)}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = build_executable()
    sys.exit(0 if success else 1)